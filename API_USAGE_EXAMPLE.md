# Interface Log Management API Usage

## Endpoints

### 1. Query Interface Logs (Paginated)
```
GET /aiInterface/query
```

### 2. Create Interface Log
```
POST /aiInterface/create
```

### 3. Update Interface Log
```
PUT /aiInterface/update
```

### 4. Scan Interface Logs
```
POST /aiInterface/scan
```

## 1. Query Interface Logs

### Parameters
- `pageNum` (optional, default: 1): Page number for pagination
- `pageSize` (optional, default: 10): Number of records per page

### Request Examples

#### Basic Request
```bash
curl -X GET "http://localhost:8080/aiInterface/query"
```

#### With Pagination Parameters
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=20"
```

## 2. Create Interface Log

### Parameters
- `interfaceName` (required): The name/path of the interface

### Request Examples

#### Create New Interface Log
```bash
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/login"
```

#### Using JSON (if needed)
```bash
curl -X POST "http://localhost:8080/aiInterface/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "interfaceName=/api/user/profile"
```

## 3. Update Interface Log

### Parameters
- `id` (required): Unique identifier of the record to update
- `interfaceName` (required): New interface name
- `context` (optional): New context value
- `flag` (optional): New flag value (0 or 1)
- `scaned` (optional): New scaned value (0 or 1)

### Request Examples

#### Update All Fields
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=123&interfaceName=/api/user/login&context=Updated context&flag=1&scaned=1"
```

#### Update Only Required Fields
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=123&interfaceName=/api/user/updated"
```

## 4. Scan Interface Logs

### Parameters
- `interfaceName` (optional): The name/path of the interface to scan

### Request Examples

#### Scan Specific Interface
```bash
curl -X POST "http://localhost:8080/aiInterface/scan" \
  -d "interfaceName=/api/user/login"
```

#### Scan All Unscanned Interfaces
```bash
curl -X POST "http://localhost:8080/aiInterface/scan"
```

## Response Formats

### Query Response (Success)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": [
    {
      "id": 12345,
      "interfaceName": "/api/user/login",
      "context": "User login request",
      "flag": 1,
      "scaned": 0
    }
  ],
  "page": 1,
  "size": 10,
  "total": 150
}
```

### Create Response (Success)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志记录创建成功"
}
```

### Create Response (Interface Already Exists)
```json
{
  "code": 400,
  "message": "该接口已存在",
  "success": false,
  "data": null
}
```

### Update Response (Success)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志记录更新成功"
}
```

### Update Response (Record Not Found)
```json
{
  "code": 404,
  "message": "记录不存在",
  "success": false,
  "data": null
}
```

### Scan Response (Specific Interface - Success)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志扫描完成，处理了 1 条记录",
  "processedCount": 1
}
```

### Scan Response (Batch Scan - Success)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "批量扫描完成，处理了 5 条记录",
  "processedCount": 5
}
```

### Scan Response (Interface Not Found)
```json
{
  "code": 404,
  "message": "指定的接口记录不存在或扫描失败",
  "success": false,
  "data": null,
  "processedCount": 0
}
```

### General Success Response
### General Error Response
```json
{
  "code": 500,
  "message": "Error message details",
  "success": false,
  "data": null
}
```

### Validation Error Response
```json
{
  "code": 400,
  "message": "接口名称不能为空",
  "success": false,
  "data": null
}
```

## Frontend Usage

### JavaScript/TypeScript Examples

#### Query Interface Logs
```javascript
async function fetchInterfaceLogs(pageNum = 1, pageSize = 10) {
  try {
    const response = await fetch(`/aiInterface/query?pageNum=${pageNum}&pageSize=${pageSize}`);
    const result = await response.json();
    
    if (result.success) {
      console.log('Data:', result.data);
      console.log('Total records:', result.total);
      console.log('Current page:', result.page);
      
      // Process the interface log data
      result.data.forEach(log => {
        console.log(`Interface: ${log.interfaceName}, Context: ${log.context}`);
      });
    } else {
      console.error('API Error:', result.message);
    }
  } catch (error) {
    console.error('Network Error:', error);
  }
}

// Usage
fetchInterfaceLogs(1, 20);

#### Create Interface Log
```javascript
async function createInterfaceLog(interfaceName) {
  try {
    const formData = new FormData();
    formData.append('interfaceName', interfaceName);

    const response = await fetch('/aiInterface/create', {
      method: 'POST',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      console.log('Interface log created successfully');
      return true;
    } else {
      console.error('Creation failed:', result.message);
      return false;
    }
  } catch (error) {
    console.error('Network Error:', error);
    return false;
  }
}

// Usage
createInterfaceLog('/api/user/login');

#### Update Interface Log
```javascript
async function updateInterfaceLog(id, interfaceName, context = null, flag = null, scaned = null) {
  try {
    const formData = new FormData();
    formData.append('id', id);
    formData.append('interfaceName', interfaceName);
    if (context !== null) formData.append('context', context);
    if (flag !== null) formData.append('flag', flag);
    if (scaned !== null) formData.append('scaned', scaned);

    const response = await fetch('/aiInterface/update', {
      method: 'PUT',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      console.log('Interface log updated successfully');
      return true;
    } else {
      console.error('Update failed:', result.message);
      return false;
    }
  } catch (error) {
    console.error('Network Error:', error);
    return false;
  }
}

// Usage
updateInterfaceLog(123, '/api/user/profile', 'Updated context', 1, 0);

#### Scan Interface Logs
```javascript
async function scanInterfaceLogs(interfaceName = null) {
  try {
    const formData = new FormData();
    if (interfaceName) {
      formData.append('interfaceName', interfaceName);
    }

    const response = await fetch('/aiInterface/scan', {
      method: 'POST',
      body: formData
    });
    const result = await response.json();

    if (result.success) {
      console.log('Scan completed successfully');
      console.log('Message:', result.data);
      console.log('Processed count:', result.processedCount);
      return result.processedCount;
    } else {
      console.error('Scan failed:', result.message);
      return 0;
    }
  } catch (error) {
    console.error('Network Error:', error);
    return 0;
  }
}

// Usage - Scan specific interface
scanInterfaceLogs('/api/user/login');

// Usage - Scan all unscanned interfaces
scanInterfaceLogs();
```

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

function useInterfaceLogs(pageNum = 1, pageSize = 10) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/aiInterface/query?pageNum=${pageNum}&pageSize=${pageSize}`);
        const result = await response.json();
        
        if (result.success) {
          setData(result.data);
          setTotal(result.total);
          setError(null);
        } else {
          setError(result.message);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [pageNum, pageSize]);

  return { data, loading, error, total };
}
```

## Data Fields Description

- **id**: Unique identifier for the interface log record
- **interfaceName**: Name/path of the interface that was called
- **context**: Additional context or description about the interface call
- **flag**: Status flag (0 or 1) indicating some processing state
- **scaned**: Scan status (0 or 1) indicating whether the log has been processed

## Database Information

- **Database**: REPEATER
- **Table**: interface_log
- **Ordering**: Results are ordered by ID in descending order (newest first)
