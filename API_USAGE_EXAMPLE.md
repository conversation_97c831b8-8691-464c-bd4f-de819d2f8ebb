# Interface Log Query API Usage

## Endpoint
```
GET /aiInterface/query
```

## Parameters
- `pageNum` (optional, default: 1): Page number for pagination
- `pageSize` (optional, default: 10): Number of records per page

## Request Examples

### Basic Request
```bash
curl -X GET "http://localhost:8080/aiInterface/query"
```

### With Pagination Parameters
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=20"
```

## Response Format

### Success Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": [
    {
      "id": 12345,
      "interfaceName": "/api/user/login",
      "context": "User login request",
      "flag": 1,
      "scaned": 0
    },
    {
      "id": 12344,
      "interfaceName": "/api/user/register",
      "context": "User registration request",
      "flag": 0,
      "scaned": 1
    }
  ],
  "page": 1,
  "size": 10,
  "total": 150
}
```

### Error Response
```json
{
  "code": 500,
  "message": "Error fetching interface logs: Database connection failed",
  "success": false,
  "data": null
}
```

## Frontend Usage

### JavaScript/TypeScript Example
```javascript
async function fetchInterfaceLogs(pageNum = 1, pageSize = 10) {
  try {
    const response = await fetch(`/aiInterface/query?pageNum=${pageNum}&pageSize=${pageSize}`);
    const result = await response.json();
    
    if (result.success) {
      console.log('Data:', result.data);
      console.log('Total records:', result.total);
      console.log('Current page:', result.page);
      
      // Process the interface log data
      result.data.forEach(log => {
        console.log(`Interface: ${log.interfaceName}, Context: ${log.context}`);
      });
    } else {
      console.error('API Error:', result.message);
    }
  } catch (error) {
    console.error('Network Error:', error);
  }
}

// Usage
fetchInterfaceLogs(1, 20);
```

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

function useInterfaceLogs(pageNum = 1, pageSize = 10) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/aiInterface/query?pageNum=${pageNum}&pageSize=${pageSize}`);
        const result = await response.json();
        
        if (result.success) {
          setData(result.data);
          setTotal(result.total);
          setError(null);
        } else {
          setError(result.message);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [pageNum, pageSize]);

  return { data, loading, error, total };
}
```

## Data Fields Description

- **id**: Unique identifier for the interface log record
- **interfaceName**: Name/path of the interface that was called
- **context**: Additional context or description about the interface call
- **flag**: Status flag (0 or 1) indicating some processing state
- **scaned**: Scan status (0 or 1) indicating whether the log has been processed

## Database Information

- **Database**: REPEATER
- **Table**: interface_log
- **Ordering**: Results are ordered by ID in descending order (newest first)
