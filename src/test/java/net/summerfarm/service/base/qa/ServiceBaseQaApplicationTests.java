package net.summerfarm.service.base.qa;

import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import net.summerfarm.service.base.qa.DTO.ESCaseDto;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.rpc.service.GenericService;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@SpringBootTest
class ServiceBaseQaApplicationTests {

    @Value("${dubbo.registry.address}")
    private String serverAddr;

    @Resource
    private RedisTemplate<String, Object> redisTemplateDB5;



    @Test
    void contextLoads() {
        System.out.println(serverAddr);

        ApplicationConfig application = new ApplicationConfig();
        application.setName("ATP");

        RegistryConfig registry = new RegistryConfig();
        registry.setAddress("test-nacos.summerfarm.net:11000");
        registry.setProtocol("nacos");
        registry.setParameters(Collections.singletonMap("namespace", "fac8164c-1da8-43d2-bf49-e187bda7fcb4"));

        ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
        application.setRegistry(registry);
        reference.setApplication(application);
        reference.setInterface("net.summerfarm.wnc.client.provider.delivery.DeliveryRuleQueryProvider");
        reference.setGeneric(true);
        reference.setVersion("1.0.0");
        reference.setGroup("online");
        reference.getServices();
        reference.setTag("dev");
        GenericService genericService = reference.get();
        Map<String, Object> map = JSON.parseObject("{\"contactId\":342497,\"merchantId\":344054,\"orderTime\":\"2025-04-16T10:35:16.948\",\"source\":\"XM_SAMPLE_APPLY\"}");
        // 调用远程方法
        Object result = genericService.$invoke("queryDeliveryDateInfo", new String[]{"net.summerfarm.wnc.client.req.DeliveryRuleQueryReq"}, new Object[]{map});

        System.out.println(result);
    }

    @Test
    void test() {
        DataType type = redisTemplateDB5.type("login:mall:mall__0c483bc7-e1c5-4085-bfd0-adf06d4bef9f");
        System.out.println("redisInvoke type:{}"+type);
        Object code = redisTemplateDB5.opsForValue().get("login:mall:mall__0149732a-01a4-49e6-bb7e-69488b55f72f");
        // 获取整个哈希
//        Map<Object, Object> entries = redisTemplate.opsForHash().entries("es:area_sku:102933");
//
//// 获取特定字段值
//        Object value = redisTemplate.opsForHash().get("es:area_sku:102933", "fieldName");

        System.out.println(code);
    }

    @Test
    void test1() throws Exception {
//        esService.insertToES("test", "test");
        String key= "test-opera1";
        ESCaseDto data = new ESCaseDto();
        data.setTestPoints("test");
        data.setPrompt("pp");
        data.setTestPoints("test");
//        esService.insertToES(key, data.getProjectId(), data.getPrompt(), data.getResponse(), data.getTestPoints());
//        esService.editInsertToES(key, "test22", "update");
    }

}
