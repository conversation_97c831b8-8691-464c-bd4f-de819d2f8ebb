package net.summerfarm.service.base.qa.service.datachecker.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.domain.goodsdb.BackCategory;
import net.summerfarm.service.base.qa.domain.xianmudb.Category;
import net.summerfarm.service.base.qa.domain.xianmudb.TaxRateConfig;
import net.summerfarm.service.base.qa.mapper.goodsdb.BackCategoryMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.CategoryMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.TaxRateConfigMapper;
import net.summerfarm.service.base.qa.service.datachecker.ProductInventoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.testng.annotations.IFactoryAnnotation;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.LongAdder;

@Service
@Slf4j
public class ProductInventoryServiceImpl implements ProductInventoryService {

    @Resource
    BackCategoryMapper backCategoryMapper;

    @Resource
    CategoryMapper categoryMapper;

    @Resource
    TaxRateConfigMapper taxRateConfigMapper;

    @Override
    public Map<String, String> ProductInventoryChecker() {
        Map<String, String> map = new HashMap<>();
        //检查总数是否一致
        LambdaQueryWrapper<BackCategory> backCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<BackCategory> backCategories = backCategoryMapper.selectList(backCategoryLambdaQueryWrapper);
        LambdaQueryWrapper<Category> categoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<Category> categories = categoryMapper.selectList(categoryLambdaQueryWrapper);
        LambdaQueryWrapper<TaxRateConfig> taxRateConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<TaxRateConfig> taxRateConfigs = taxRateConfigMapper.selectList(taxRateConfigLambdaQueryWrapper);
        if (backCategories.size() != categories.size()){
            map.put("back_category --> category: 数量不一致：",  backCategories.size() + " --> " + categories.size());
        }
        LongAdder countCheck = new LongAdder();
        backCategories.forEach(backCategory -> {
            categories.forEach(category -> {
                if (backCategory.getId().equals(Long.valueOf(String.valueOf(category.getId())))){
                    countCheck.add(1);
                    //code检查
                    String categoryId = "" + category.getId();
                    if (category.getId() < 10){
                        categoryId = "00" + categoryId ;
                    }else if (category.getId() < 100){
                        categoryId = "0" + categoryId ;
                    }
                    String code = "HT" + backCategory.getTenantId() + categoryId;
                    if (!backCategory.getCode().equals(code)){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : code",  backCategory.getCode() + " --> " + code);
                    }
                    //parentId检查
                    if (!backCategory.getParentId().equals(Objects.isNull(category.getParentId()) ? 0L : Long.valueOf(String.valueOf(category.getParentId())))){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : parent_id",  backCategory.getParentId() + " --> " + category.getParentId());
                    }
                    Integer level = 1;
                    //level检查
                    if (!backCategory.getParentId().equals(0L)){
                        //获取上级的上级id，如果上级的上级id为0，则上级是一级，那当前为2级；否则为3级
                        Long parentId = backCategories.stream().filter(a -> a.getId().equals(backCategory.getParentId())).map(b -> b.getParentId()).findAny().orElse(100L);
                        if (!parentId.equals(0L)){
                            level = 3;
                        }else {
                            level = 2;
                        }
                    }
                    if (!backCategory.getLevel().equals(level)){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : level",  backCategory.getLevel() + " --> " + level);
                    }
                    //nature检查
                    if (!backCategory.getNature().equals(0)){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : nature",  backCategory.getNature() + " --> " + 0);
                    }
                    //name检查
                    if (!backCategory.getName().equals(category.getCategory())){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : name",  backCategory.getName() + " --> " + category.getCategory());
                    }

                    //type检查
                    if (Objects.isNull(category.getType()) || category.getType().equals(1) || category.getType().equals(3)){
                        category.setType(5);
                    }
                    if (!backCategory.getType().equals(category.getType())){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : type",  backCategory.getType() + " --> " + category.getType());
                    }
                    //tax_rate_code、tax_rate_value检查
                    TaxRateConfig taxRateConfig = taxRateConfigs.stream().filter(a -> category.getId().equals(a.getCategoryId())).findFirst().orElse(null);
                    if (Objects.isNull(taxRateConfig)){
                        if (!StringUtils.isEmpty(backCategory.getTaxRateCode())){
                            map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : tax_rate_code",  backCategory.getTaxRateCode() + " --> " + null);
                        }
                        if (Objects.nonNull(backCategory.getTaxRateValue())){
                            map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : tax_rate_value",  backCategory.getTaxRateValue() + " --> " + null);
                        }
                    }else {
                        if ((StringUtils.isNotEmpty(backCategory.getTaxRateCode()) && StringUtils.isNotEmpty(taxRateConfig.getTaxRateCode()))
                                && !backCategory.getTaxRateCode().equals(taxRateConfig.getTaxRateCode())){
                            map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : tax_rate_code",  backCategory.getTaxRateCode() + " --> " + taxRateConfig.getTaxRateCode());
                        }
                        if ((Objects.nonNull(backCategory.getTaxRateValue()) && Objects.nonNull(taxRateConfig.getTaxRateValue()))
                                && backCategory.getTaxRateValue().compareTo(taxRateConfig.getTaxRateValue()) != 0){
                            map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : tax_rate_value",  backCategory.getTaxRateValue() + " --> " + taxRateConfig.getTaxRateValue());
                        }
                    }
                    //tenant_id 检查
                    if (!backCategory.getTenantId().equals(-1L)){
                        map.put("back_category --> category:失败数据的id: " + backCategory.getId() +" : tenant_id",  backCategory.getTenantId() + " --> " + -1);
                    }
                }
            });
        });
        if (countCheck.intValue() != backCategories.size()){
            map.put("back_category --> category: 校验数量不一致",  backCategories.size() + " --> " + countCheck);
        }
        map.put("back_category --> category: 目标总数 --> 校验总数",  backCategories.size() + " --> " + countCheck);
        return map;
    }
}
