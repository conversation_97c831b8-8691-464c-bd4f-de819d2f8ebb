package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.WmsWarehouseConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 仓库配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-234 11:02:49
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface WmsWarehouseConfigMapper extends BaseMapper<WmsWarehouseConfig> {

}
