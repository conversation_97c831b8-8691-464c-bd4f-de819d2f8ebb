package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.WmsStockTaskNoticeOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 出库通知单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-313 13:53:45
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface WmsStockTaskNoticeOrderMapper extends BaseMapper<WmsStockTaskNoticeOrder> {

}
