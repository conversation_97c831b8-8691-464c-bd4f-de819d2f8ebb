package net.summerfarm.service.base.qa.model.esRespo;

import net.summerfarm.service.base.qa.model.AICaseEdit;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2025-04-15
 */
@Repository
public interface AIEditRepository extends ElasticsearchRepository<AICaseEdit, String> {
}
