package net.summerfarm.service.base.qa.utils;/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2025-04-15
 */

import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @ClassName ElasticsearchConfig
 * @date 2025-04-15
 */
@Configuration
public class ElasticsearchConfig {

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        ClientConfiguration configuration = ClientConfiguration.builder()
                .connectedTo("dev.es.summerfarm.net:80")
                .withBasicAuth("elastic", "Xianmu619") // 如果需要认证
                .build();
        System.out.println("手动配置");
        return RestClients.create(configuration).rest();
    }
}
