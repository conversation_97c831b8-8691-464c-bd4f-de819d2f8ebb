package net.summerfarm.service.base.qa.service.supply;

import com.alibaba.druid.sql.visitor.functions.If;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.mapper.ofcdb.FulfillmentOrderMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.*;
import net.summerfarm.service.base.qa.model.*;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.apache.ibatis.annotations.Param;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TmsService  {
    @Resource
    private TmsClearService tmsClearService;
    @Resource
    private FulfillmentOrderMapper fulfillmentOrderMapper;
    @Resource
    private TokenService tokenService;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;

    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);
    @Resource
    private ConfigValue configValue;
    @Resource
    private TmsDeliveryBatchMapper tmsDeliveryBatchMapper;

    @Resource
    private TmsMapper tmsMapper;
    public String tmsCutoff(String order,String complete)throws Exception{
        //1、先查出订单所属于的城配仓，以及配送时间
        LambdaQueryWrapper<FulfillmentOrder> ofcOrderWrapper = new LambdaQueryWrapper<>();
        ofcOrderWrapper.eq(FulfillmentOrder::getOutOrderNo, order);
        List<FulfillmentOrder> ofcOrderDb = fulfillmentOrderMapper.selectList(ofcOrderWrapper);
        if (ofcOrderDb.isEmpty()){
            return "TMS 单号不存在";
        }
        Integer storeNo = ofcOrderDb.get(0).getStoreNo();
        Integer orderStatus = ofcOrderDb.get(0).getStatus();
        if (orderStatus==-1){
            return "该订单已经取消，无法进行排线";
        }
        LocalDate fulfillmentTime =  ofcOrderDb.get(0).getFulfillmentTime();
        String mid = ofcOrderDb.get(0).getOutClientId();
        String clearCutoff = tmsClearService.clearCutoff(storeNo.toString(),fulfillmentTime.toString());
        //获取店铺名称
        //获取店铺名称
        LambdaQueryWrapper<Merchant> MerchantWrapper = new LambdaQueryWrapper<>();
        MerchantWrapper.eq(Merchant::getMId, mid);
        List<Merchant> merchantDbs = merchantMapper.selectList(MerchantWrapper);
        String mname = merchantDbs.get(0).getMname();

        //String mname = ofcOrderDb.get(0).getOutClientName();
        LocalDate preDay = fulfillmentTime.minusDays(2);
        LocalDate yesterday = fulfillmentTime.minusDays(1);
        //判断日期是否是T+2，如果是T+2做不了排线
        LocalDate currentDate = LocalDate.now();
        if (yesterday.isAfter(currentDate)) {
            return "T+1后日期无法排线";
        }
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Token",token);
        //查询店铺数据
        JSONObject siteBody = new JSONObject();
        siteBody.put("storeNo",storeNo);
        siteBody.put("endDate",yesterday);
        siteBody.put("mname",mname);
        String siteResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/tms-new/city-delivery-batch/query/delivery-site", siteBody.toString(), headers);
        JSONObject siteing = new JSONObject(siteResult);
        if (!siteing.getString("status").equals("200")){
            return "TMS查询店铺失败";
        }
        JSONObject siteStrs = new JSONObject(siteing.get("data").toString());
        //店铺信息
        JSONArray siteLists = new JSONArray(siteStrs.get("unSort").toString());
        JSONObject unSort = new JSONObject(siteLists.get(0).toString());
        String siteId =  unSort.get("id").toString();
        String poiNote = unSort.getString("poiNote");
        //Integer average =Integer.valueOf(siteStrs.get("average").toString());
        //3、排线，修改城配仓截单时间
        LambdaQueryWrapper<WarehouseLogisticsCenter> logisticsWrapper = new LambdaQueryWrapper<>();
        logisticsWrapper.eq(WarehouseLogisticsCenter::getStoreNo, storeNo);
        List<WarehouseLogisticsCenter> logisticsDb = warehouseLogisticsCenterMapper.selectList(logisticsWrapper);
        String closeTime = logisticsDb.get(0).getCloseTime();
        WarehouseLogisticsCenter entityLogisticsCenter = new WarehouseLogisticsCenter();
        entityLogisticsCenter.setCloseTime("01:00:00");
        warehouseLogisticsCenterMapper.update(entityLogisticsCenter, logisticsWrapper);

        //4、移除已经排线的数据
        //5、万能司机切换城配仓
        String driverBody ="{\n" +
                "    \"idCardFrontPic\": \"test/e1ia23zixvrex1oji.png\",\n" +
                "    \"driverPics\": \"test/mfl2xxyqd0eex1r08.png\",\n" +
                "    \"idCardBehindPic\": \"test/ibenjdy9poex1lzf.png\",\n" +
                "    \"name\": \"万能司机\",\n" +
                "    \"password\": \"123456\",\n" +
                "    \"cooperationCycle\": 1,\n" +
                "    \"idCard\": \"123456780123456789\",\n" +
                "    \"phone\": \"***********\",\n" +
                "    \"status\": 0,\n" +
                "    \"businessType\": 2,\n" +
                "    \"id\": 6012609,\n" +
                "    \"cityMappingId\": 6220,\n" +
                "    \"cityStoreNo\": "+storeNo+",\n" +
                "    \"cityCarrierId\": 3,\n" +
                "    \"cityCarId\": 8206,\n" +
                "    \"cityCarDto\": {\n" +
                "        \"adminId\": 123,\n" +
                "        \"carNumber\": \"新A12345\",\n" +
                "        \"carStorageEnum\": \"normal\",\n" +
                "        \"createTime\": \"2024-04-08 18:14:19\",\n" +
                "        \"id\": 8206,\n" +
                "        \"quantity\": 100,\n" +
                "        \"status\": 0,\n" +
                "        \"storage\": 0,\n" +
                "        \"storageDesc\": \"常温\",\n" +
                "        \"type\": 0,\n" +
                "        \"typeDesc\": \"小面包车\",\n" +
                "        \"volume\": 2.5,\n" +
                "        \"weight\": 30,\n" +
                "        \"init\": true\n" +
                "    },\n" +
                "    \"driverAccountDTO\": {\n" +
                "        \"account\": \"\",\n" +
                "        \"accountAscription\": \"\",\n" +
                "        \"accountBank\": \"\",\n" +
                "        \"accountName\": \"\"\n" +
                "    }\n" +
                "}";

        String driverResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/tms-new/driver/edit", driverBody.toString(), headers);

        //6、添加路线
        String pathBody  ="storeNo="+storeNo+"&deliveryTime="+fulfillmentTime+"";
        String pathSaveResult = okHttpUtils.sendFormPost(configValue.summerfarmManageUri + "/tms-new/city-delivery-batch/upsert/batch-path-Save", pathBody, headers);
        JSONObject pathSave = new JSONObject(pathSaveResult);
        if (!pathSave.getString("status").equals("200")){
            return "TMS 路线添加失败";
        }
        JSONObject pathSaveStr = new JSONObject(pathSave.get("data").toString());
        String pathCode = pathSaveStr.getString("pathCode");
        String pathId =  pathSaveStr.getString("pathId");

        //7、重新排线
        JSONObject updateSiteBody =  new JSONObject();
        updateSiteBody.put("id",siteId);
        updateSiteBody.put("path",pathCode);
        updateSiteBody.put("storeNo",storeNo);
        updateSiteBody.put("sendWay",0);
        String updateSiteResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/tms-new/city-delivery-batch/upsert/update-site-batch", updateSiteBody.toString(), headers);
        System.out.println(updateSiteResult);
        JSONObject updateSiteResults = new JSONObject(updateSiteResult);
        if (!updateSiteResults.getString("status").equals("200")){
            return "TMS 排线失败";
        }
        //查询路线
        LambdaQueryWrapper<TmsDeliveryBatch> tmsDeliveryWrapper = new LambdaQueryWrapper<>();
        tmsDeliveryWrapper.eq(TmsDeliveryBatch::getPathId,pathId).eq(TmsDeliveryBatch::getPathCode,pathCode).orderByDesc(TmsDeliveryBatch::getId);
        List<TmsDeliveryBatch> tmsDeliveryDb = tmsDeliveryBatchMapper.selectList(tmsDeliveryWrapper);
        long tmsDeliveryId =  tmsDeliveryDb.get(0).getId();

        //8、添加司机
        JSONObject deliverBody =new JSONObject();
        deliverBody.put("id",tmsDeliveryId);
        deliverBody.put("deliveryCarId",6012609);
        deliverBody.put("storeNo",storeNo);
        String deliverResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/tms-new/city-delivery-batch/upsert/update-path-driver", deliverBody.toString(), headers);
        JSONObject deliverResults = new JSONObject(deliverResult);
        if (!deliverResults.getString("status").equals("200")){
            return "TMS 司机添加失败";
        }

        //11、完成排线
        String completeBody = "{\n" +
                "    \"storeNo\": "+storeNo+",\n" +
                "    \"startDate\": \""+preDay+"\",\n" +
                "    \"endDate\": \""+yesterday+"\",\n" +
                "    \"deliveryCarPath\": [\n" +
                "        {\n" +
                "            \"storeNo\": "+storeNo+",\n" +
                "            \"totalNum\": 1,\n" +
                "            \"path\": \""+pathCode+"\",\n" +
                "            \"deliveryTime\": \""+fulfillmentTime+"\",\n" +
                "            \"deliveryCarId\": 6012609,\n" +
                "            \"totalDistance\": 19.28\n" +
                "        }\n" +
                "    ]\n" +
                "}\n";
        token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        headers.put("Token",token);
        ///这边数据没删，等数据删了再代码完成排线
        String completeResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri+"/tms-new/city-delivery-batch/upsert/complete-path", completeBody.toString(), headers);
        System.out.println(completeResult);
        JSONObject completeResults = new JSONObject(completeResult);
        if (!completeResults.getString("status").equals("200")){
            return "TMS 完成排线失败";
        }
        try {
            // 使用 TimeUnit 让线程睡眠2秒
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //判断是否只需要排线数据即可
        if (!complete.isEmpty()){
            return "排线完成";
        }
        //13、登录小程序
        JSONObject driver = new JSONObject();
        driver.put("username","***********");
        driver.put("password","123456");
        driver.put("authId","ofaF65YX_VxFuJewFsffE_LbAqMM,ok4fcwYXS7_xZCaaY_305Abhm3H0");
        driver.put("origin",1);
        driver.put("authType",1);
        String driversResult = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/authentication/auth/unified/authorized/bind", driver.toString(), headers);
        JSONObject drivers = new JSONObject(driversResult);
        if(!drivers.getString("code").equals("SUCCESS")){
            return "TMS 小程序登录失败";
        }
        JSONObject driversStr = new JSONObject(drivers.get("data").toString());
        String driverToken = driversStr.get("token").toString();
        headers.put("Token",driverToken);
        //14、查看拣货单列表
        String listBody="{\"pageSize\": 30,\"pageIndex\": 1}";
        String pickList = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-batch/query/pick-list", listBody, headers);
        JSONObject pickLists = new JSONObject(pickList);
        JSONObject pickData = new JSONObject(pickLists.get("data").toString());
        JSONArray pickListData = new JSONArray(pickData.getString("list"));
        JSONObject pick = new JSONObject(pickListData.getString(0));
        String batchId =pick.getString("batchId");

        String detailBody= "{\"batchId\": \""+batchId+"\"}";
        String detailList = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-batch/query/pick-detail", detailBody, headers);
        JSONObject detailLists = new JSONObject(detailList);
        if(!detailLists.getString("code").equals("SUCCESS")){
            return "TMS 获取拣货单失败";
        }
        JSONArray detailDatas = new JSONArray(detailLists.get("data").toString());
       //------
        JSONArray deliveryPickDetails = new JSONArray();
        for (int i=0;i<detailDatas.length();i++){
            JSONObject detailData = new JSONObject(detailDatas.getString(i));
            siteId = detailData.getString("deliverySiteId");
            JSONArray pickDetails = new JSONArray(detailData.getString("pickDetails"));
            for (int j=0;j<pickDetails.length();j++){
                JSONObject pickDetail = pickDetails.getJSONObject(j);
                JSONObject deliveryPickDetail =new JSONObject();
                deliveryPickDetail.put("detailStatus",1);
                deliveryPickDetail.put("sku",pickDetail.get("sku"));
                deliveryPickDetail.put("id",pickDetail.get("id"));
                deliveryPickDetail.put("batchId",batchId);
                deliveryPickDetails.put(deliveryPickDetail);
            }

        }

        JSONObject finishBody = new JSONObject();
        finishBody.put("deliveryPickDetails",deliveryPickDetails);
        finishBody.put("deliverySiteId",siteId);
        String finishList = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-site/upsert/finish-pick", finishBody.toString(), headers);
        //15、查看拣货单明细
        String siteListBody ="{\"batchId\": \""+batchId+"\"}";
        String siteListResult  = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-batch/query/delivery-site-list", siteListBody, headers);
        JSONObject siteListLists = new JSONObject(siteListResult);

        JSONArray siteListDatas = new JSONArray(siteListLists.get("data").toString());
        JSONObject siteListData = new JSONObject(siteListDatas.getString(0));
        siteId = siteListData.getString("deliverySiteId");

        try {
            // 使用 TimeUnit 让线程睡眠2秒
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //16、拣货
        //17、配送详情
        String deliveryResult  = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-site/query/delivery-site-detail?deliverySiteId="+siteId+"", "{}", headers);
        JSONObject deliveryLists = new JSONObject(deliveryResult);
        JSONObject deliveryData = new JSONObject(deliveryLists.get("data").toString());
        JSONArray orderItemLists = new JSONArray(deliveryData.getString("orderItemList"));

        for(int j=0;j<orderItemLists.length();j++){
            JSONObject orderItemList = orderItemLists.getJSONObject(j);
            JSONObject codeSku = new JSONObject();
            codeSku.put("sku",orderItemList.getString("sku"));
            codeSku.put("quantity",orderItemList.getInt("amount"));
            codeSku.put("pictures","test/6jsejnpbjqs6tuci8t.png");
            codeSku.put("deliverySiteId",deliveryData.getString("deliverySiteId"));
            codeSku.put("deliverySiteItemId",orderItemList.getString("deliverySiteItemId"));
            codeSku.put("remake","扫码/无码");
            String noCodeSkuResult  = okHttpUtils.sendCustomJsonPost("https://devtms.summerfarm.net/tms-new/city-delivery-site/upsert/add-no-code-sku",codeSku.toString() , headers);
            String CodeSkuResult  = okHttpUtils.sendCustomJsonPost(" https://devtms.summerfarm.net/tms-new/city-delivery-site/query/delivery-site-detail?deliverySiteId="+orderItemList.getString("deliverySiteItemId")+"",codeSku.toString() , headers);
        }
        //完成配送
        JSONObject finishDeliveryBody = new JSONObject();
        finishDeliveryBody.put("finishPoi",poiNote);
        finishDeliveryBody.put("id",siteId);
        finishDeliveryBody.put("signForStatus",0);
        finishDeliveryBody.put("signForRemarks","");
        finishDeliveryBody.put("outDistance",0);
        finishDeliveryBody.put("finishDistance",0);
        finishDeliveryBody.put("deliverySiteId",siteId);
        finishDeliveryBody.put("deliveryPic","test/6jsejnpbjqs6tuci8t.png");
        finishDeliveryBody.put("signPic","test/6jsejnpbjqs6tuci8t.png");
        finishDeliveryBody.put("productPic","test/6jsejnpbjqs6tuci8t.png");
        String finishDeliveryResult  = okHttpUtils.sendCustomJsonPost(" https://devtms.summerfarm.net/tms-new/city-delivery-site/upsert/finish-delivery-site",finishDeliveryBody.toString() , headers);
        JSONObject finishDeliveryResults = new JSONObject(finishDeliveryResult);
        if(!finishDeliveryResults.getString("status").equals("200")){
            return "TMS 卸货失败";
        }
         clearCutoff = tmsClearService.clearCutoff(storeNo.toString(),fulfillmentTime.toString());
        //12、城配仓时间恢复
        entityLogisticsCenter.setCloseTime("22:00:00");
        warehouseLogisticsCenterMapper.update(entityLogisticsCenter, logisticsWrapper);

        return "执行成功";
    }

}
