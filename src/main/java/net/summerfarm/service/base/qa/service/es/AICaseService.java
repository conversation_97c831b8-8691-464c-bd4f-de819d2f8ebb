package net.summerfarm.service.base.qa.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.service.base.qa.mapper.ofcdb.AICaseMapper;
import net.summerfarm.service.base.qa.model.AICase;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.logging.Logger;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AICaseService
 * @date 2025-04-18
 */
@Service
public class AICaseService {

    private static final Logger logger = Logger.getLogger(AICaseService.class.getName());

    @Resource
    private AICaseMapper aiCaseMapper;
    
    @Resource
    private RestHighLevelClient elasticsearchClient;

    public void insertAICaseRecord(String projectId, String fileName, String operateTime) {
        AICase aiCase = new AICase();
        aiCase.setProjectId(projectId);
        aiCase.setFileName(fileName);
        Instant instant = Instant.parse(operateTime);
        // 转换为LocalDateTime（默认使用系统时区，或指定时区）
        LocalDateTime localDateTime = instant.atZone(ZoneId.of("UTC")).toLocalDateTime();
        // 转换为java.sql.Timestamp
        aiCase.setCreateTime(localDateTime);
        aiCase.setPhase("1");
        aiCaseMapper.insert(aiCase);
    }

    public int updateAICaseRecord(AICase aiCase) {
        return aiCaseMapper.updateById(aiCase);
    }
    
    /**
     * 每分钟执行一次，查询status为0的AICase记录，并在ES中查询相关数据
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void processZeroStatusCases() {
        List<AICase> zeroStatusCases = findCasesWithZeroStatus();
        
        if (zeroStatusCases.isEmpty()) {
            logger.info("No cases with status 0 found");
            return;
        }
        
        logger.info("Found " + zeroStatusCases.size() + " cases with status 0");
        
        for (AICase aiCase : zeroStatusCases) {
            try {
                String projectId = aiCase.getProjectId();
                SearchResponse searchResponse = searchESByProjectId(projectId);
                
                // 处理搜索结果
                processSearchResults(aiCase, searchResponse);
                
                // 更新案例状态为已处理
                aiCase.setStatus(1);
                updateAICaseRecord(aiCase);
                
            } catch (IOException e) {
                logger.severe("Error searching ES for projectId " + aiCase.getProjectId() + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * 根据projectId在Elasticsearch中查询数据
     */
    private SearchResponse searchESByProjectId(String projectId) throws IOException {
        // 搜索ai_case、ai_point_case和ai_case_edit_record索引
        SearchRequest searchRequest = new SearchRequest("ai_case", "ai_point_case", "ai_case_edit_record");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchQuery("projectId", projectId));
        searchRequest.source(searchSourceBuilder);
        
        return elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);
    }
    
    /**
     * 处理ES搜索结果，比较ai_case中的response和ai_point_case中的selectedTestPoints的差异
     */
    private void processSearchResults(AICase aiCase, SearchResponse searchResponse) {
        SearchHits hits = searchResponse.getHits();
        long totalHits = hits.getTotalHits().value;
        logger.info("Found " + totalHits + " ES documents for projectId: " + aiCase.getProjectId());
        
        if (totalHits == 0) {
            logger.info("No documents found for projectId: " + aiCase.getProjectId());
            return;
        }
        
        JSONArray aiCaseResponse = null;
        JSONArray pointCaseSelectedPoints = null;
        List<Map<String, String>> editRecords = new ArrayList<>();
        
        // 提取ai_case、ai_point_case和ai_case_edit_record中的数据
        for (SearchHit hit : hits) {
            String indexName = hit.getIndex();
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            
            if (indexName.equals("ai_case") && sourceAsMap.containsKey("response")) {
                try {
                    String responseStr = sourceAsMap.get("response").toString();
                    aiCaseResponse = JSON.parseArray(responseStr);
                    logger.info("Found response JSON array in ai_case with " + aiCaseResponse.size() + " items");
                } catch (Exception e) {
                    logger.warning("Failed to parse response as JSONArray: " + e.getMessage());
                }
            } else if (indexName.equals("ai_point_case") && sourceAsMap.containsKey("selectedTestPoints")) {
                try {
                    Object selectedPoints = sourceAsMap.get("selectedTestPoints");
                    String selectedPointsStr = selectedPoints.toString();
                    pointCaseSelectedPoints = JSON.parseArray(selectedPointsStr);
                    logger.info("Found selectedTestPoints JSON array in ai_point_case with " + pointCaseSelectedPoints.size() + " items");
                } catch (Exception e) {
                    logger.warning("Failed to parse selectedTestPoints as JSONArray: " + e.getMessage());
                }
            } else if (indexName.equals("ai_case_edit_record")) {
                // 提取编辑记录
                try {
                    Map<String, String> editRecord = new HashMap<>();
                    if (sourceAsMap.containsKey("operation")) {
                        editRecord.put("operation", sourceAsMap.get("operation").toString());
                    }
                    if (sourceAsMap.containsKey("originalData")) {
                        editRecord.put("originalData", sourceAsMap.get("originalData").toString());
                    }
                    if (sourceAsMap.containsKey("operateTime")) {
                        editRecord.put("operateTime", sourceAsMap.get("operateTime").toString());
                    }
                    editRecords.add(editRecord);
                    logger.info("Found edit record with operation: " + editRecord.get("operation"));
                } catch (Exception e) {
                    logger.warning("Failed to process edit record: " + e.getMessage());
                }
            }
        }
        
        // 比较两个JSONArray的差异
        if (aiCaseResponse != null && pointCaseSelectedPoints != null) {
            findDifferences(aiCaseResponse, pointCaseSelectedPoints, aiCase, editRecords);
        } else {
            logger.warning("Could not find both JSON arrays for comparison");
        }
    }
    
    /**
     * 比较两个JSONArray的差异，特别是找出id有差异的子项
     */
    private void findDifferences(JSONArray aiCaseArray, JSONArray pointCaseArray, AICase aiCase, List<Map<String, String>> editRecords) {
        // 创建一个Map来存储aiCaseArray中的项，用于快速查找，以id为键
        Map<String, Object> aiCaseMap = new HashMap<>();
        for (int i = 0; i < aiCaseArray.size(); i++) {
            Object item = aiCaseArray.get(i);
            if (item instanceof JSONObject && ((JSONObject) item).containsKey("id")) {
                String id = ((JSONObject) item).getString("id");
                aiCaseMap.put(id, item);
            } else {
                // 如果没有id字段，则使用整个JSON字符串作为键
                aiCaseMap.put(JSON.toJSONString(item), item);
            }
        }
        
        // 查找pointCaseArray中id不在aiCaseArray中的项
        List<Object> uniqueIdsInPointCase = new ArrayList<>();
        for (int i = 0; i < pointCaseArray.size(); i++) {
            Object item = pointCaseArray.get(i);
            if (item instanceof JSONObject && ((JSONObject) item).containsKey("id")) {
                String id = ((JSONObject) item).getString("id");
                if (!aiCaseMap.containsKey(id)) {
                    uniqueIdsInPointCase.add(item);
                }
            } else {
                // 如果没有id字段，则使用整个JSON字符串进行比较
                String itemJson = JSON.toJSONString(item);
                if (!aiCaseMap.containsKey(itemJson)) {
                    uniqueIdsInPointCase.add(item);
                }
            }
        }
        
        // 创建一个Map来存储pointCaseArray中的项，用于快速查找，以id为键
        Map<String, Object> pointCaseMap = new HashMap<>();
        for (int i = 0; i < pointCaseArray.size(); i++) {
            Object item = pointCaseArray.get(i);
            if (item instanceof JSONObject && ((JSONObject) item).containsKey("id")) {
                String id = ((JSONObject) item).getString("id");
                pointCaseMap.put(id, item);
            } else {
                // 如果没有id字段，则使用整个JSON字符串作为键
                pointCaseMap.put(JSON.toJSONString(item), item);
            }
        }
        
        // 查找aiCaseArray中id不在pointCaseArray中的项
        List<Object> uniqueIdsInAiCase = new ArrayList<>();
        for (int i = 0; i < aiCaseArray.size(); i++) {
            Object item = aiCaseArray.get(i);
            if (item instanceof JSONObject && ((JSONObject) item).containsKey("id")) {
                String id = ((JSONObject) item).getString("id");
                if (!pointCaseMap.containsKey(id)) {
                    uniqueIdsInAiCase.add(item);
                }
            } else {
                // 如果没有id字段，则使用整个JSON字符串进行比较
                String itemJson = JSON.toJSONString(item);
                if (!pointCaseMap.containsKey(itemJson)) {
                    uniqueIdsInAiCase.add(item);
                }
            }
        }
        
        logger.info("Found " + uniqueIdsInPointCase.size() + " items with unique IDs in selectedTestPoints-被舍弃的测试点");
        logger.info("Found " + uniqueIdsInAiCase.size() + " items with unique IDs in response-新增的测试点");
        
        // 记录具体的差异项
        if (!uniqueIdsInPointCase.isEmpty() || !uniqueIdsInAiCase.isEmpty()) {
            // 使用Hutool发送HTTP请求到AI服务
            String apiUrl = "https://test-one-api.summerfarm.top/v1/chat/completions";
            String apiKey = "sk-uXJgSOjgTlFCC2BcA172E33231204e94B9A5618089Ab9621"; // 应该从配置中获取
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "deepseek-v3-250324");
            
            List<Map<String, String>> messages = new ArrayList<>();
            
            // 添加系统消息
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "你是一个经验丰富的提示词工程专家并且拥有多年电商业务的测试经验.");
            messages.add(systemMessage);
            
            // 添加用户消息，包含差异信息
            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("请分析以下测试点的差异并给出评价以及提示词的优化建议：\n\n");
            
            if (!uniqueIdsInPointCase.isEmpty()) {
                contentBuilder.append("被舍弃的测试点：\n");
                contentBuilder.append(JSON.toJSONString(uniqueIdsInPointCase)).append("\n\n");
            }
            
            if (!uniqueIdsInAiCase.isEmpty()) {
                contentBuilder.append("新增的测试点：\n");
                contentBuilder.append(JSON.toJSONString(uniqueIdsInAiCase)).append("\n\n");
            }
            
            // 添加编辑记录信息
            if (!editRecords.isEmpty()) {
                contentBuilder.append("测试点编辑历史：\n");
                
                // 按操作类型分类
                List<Map<String, String>> addRecords = new ArrayList<>();
                List<Map<String, String>> editRecords2 = new ArrayList<>();
                List<Map<String, String>> deleteRecords = new ArrayList<>();
                
                for (Map<String, String> record : editRecords) {
                    String operation = record.get("operation");
                    if ("add".equals(operation)) {
                        addRecords.add(record);
                    } else if ("edit".equals(operation)) {
                        editRecords2.add(record);
                    } else if ("delete".equals(operation)) {
                        deleteRecords.add(record);
                    }
                }
                
                if (!addRecords.isEmpty()) {
                    contentBuilder.append("新增测试点操作：\n");
                    for (Map<String, String> record : addRecords) {
                        contentBuilder.append("- 时间: ").append(record.get("operateTime"))
                                     .append(", 内容: ").append(record.get("originalData"))
                                     .append("\n");
                    }
                    contentBuilder.append("\n");
                }
                
                if (!editRecords2.isEmpty()) {
                    contentBuilder.append("编辑测试点操作：\n");
                    for (Map<String, String> record : editRecords2) {
                        contentBuilder.append("- 时间: ").append(record.get("operateTime"))
                                     .append(", 内容: ").append(record.get("originalData"))
                                     .append("\n");
                    }
                    contentBuilder.append("\n");
                }
                
                if (!deleteRecords.isEmpty()) {
                    contentBuilder.append("删除测试点操作：\n");
                    for (Map<String, String> record : deleteRecords) {
                        contentBuilder.append("- 时间: ").append(record.get("operateTime"))
                                     .append(", 内容: ").append(record.get("originalData"))
                                     .append("\n");
                    }
                    contentBuilder.append("\n");
                }
            }

            contentBuilder.append("最初的测试点：\n");
            contentBuilder.append(JSON.toJSONString(aiCaseArray)).append("\n\n");
            
            userMessage.put("content", contentBuilder.toString());
            messages.add(userMessage);
            
            requestBody.put("messages", messages);
            
            // 发送请求
            try {
                HttpResponse response = HttpRequest.post(apiUrl)
                        .header("Authorization", "Bearer " + apiKey)
                        .header("Content-Type", "application/json")
                        .body(JSONUtil.toJsonStr(requestBody))
                        .execute();
                logger.info(response.body());
                if (response.body() != null) {
                    String responseBody = response.body();
                    logger.info("AI analysis response: " + responseBody);
                    
                    // 解析响应，提取AI的分析结果
                    try {
                        JSONObject jsonResponse = JSON.parseObject(responseBody);
                        if (jsonResponse.containsKey("choices") && jsonResponse.getJSONArray("choices").size() > 0) {
                            JSONObject choice = jsonResponse.getJSONArray("choices").getJSONObject(0);
                            if (choice.containsKey("message") && choice.getJSONObject("message").containsKey("content")) {
                                String aiAnalysis = choice.getJSONObject("message").getString("content");
                                
                                // 将AI分析结果保存到AICase记录中
                                aiCase.setReview(aiAnalysis);
                                logger.info("AI review set: " + aiAnalysis);
                                aiCase.setStatus(1);
                                updateAICaseRecord(aiCase);
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("Failed to parse AI response: " + e.getMessage());
                    }
                } else {
                    logger.warning("AI API request failed with status: " + response.getStatus() + ", body: " + response.body());
                }
            } catch (Exception e) {
                logger.warning("Error sending request to AI API: " + e.getMessage());
            }
        }
        
        if (!uniqueIdsInPointCase.isEmpty()) {
            logger.info("Unique items in selectedTestPoints: " + JSON.toJSONString(uniqueIdsInPointCase));
        }
        
        if (!uniqueIdsInAiCase.isEmpty()) {
            logger.info("Unique items in response: " + JSON.toJSONString(uniqueIdsInAiCase));
        }
        
        // 更新AICase记录的接受率和差异数量
        if (aiCaseArray.size() > 0) {
            int acceptedCount = aiCaseArray.size() - uniqueIdsInAiCase.size();
            long acceptanceRate = Math.round((double) acceptedCount / aiCaseArray.size() * 100);
            aiCase.setPointAcceptanceRate(acceptanceRate);
            logger.info(String.valueOf((long)(uniqueIdsInAiCase.size() + uniqueIdsInPointCase.size())));
            logger.info("Setting point acceptance rate to " + acceptanceRate + "% and difference count to " + 
                        (uniqueIdsInAiCase.size() + uniqueIdsInPointCase.size()));
            updateAICaseRecord(aiCase);
        }
    }

    /**
     * 查询status为0的AICase记录
     */
    public List<AICase> findCasesWithZeroStatus() {
        LambdaQueryWrapper<AICase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AICase::getStatus, 0);
        return aiCaseMapper.selectList(queryWrapper);
    }

    /**
     * 查询flag为0的AICase记录，并进行分页
     * 只返回projectId、fileName、pointAcceptanceRate、review和createTime字段
     */
    public List<Map<String, Object>> findCasesWithZeroFlag(int pageNum, int pageSize) {
        // 创建分页对象
        Page<AICase> page = new Page<>(pageNum, pageSize);
        
        // 创建查询条件
        LambdaQueryWrapper<AICase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AICase::getFlag, 0)
                   .orderByDesc(AICase::getCreateTime);
        
        // 执行分页查询
        IPage<AICase> resultPage = aiCaseMapper.selectPage(page, queryWrapper);
        
        // 转换结果，只保留指定字段
        return resultPage.getRecords().stream()
                .map(aiCase -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("projectId", aiCase.getProjectId());
                    map.put("fileName", aiCase.getFileName());
                    map.put("pointAcceptanceRate", aiCase.getPointAcceptanceRate());
                    map.put("review", aiCase.getReview());
                    map.put("createTime", aiCase.getCreateTime());
                    return map;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 统计flag为0的AICase记录总数
     */
    public long countCasesWithZeroFlag() {
        LambdaQueryWrapper<AICase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AICase::getFlag, 0);
        return aiCaseMapper.selectCount(queryWrapper);
    }
}
