package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
@DS("XIANMU_DB")
public interface TmsMapper {
    Integer beginSiteId(@Param("storeNo") String storeNo);
    Integer updateTmsDeliveryBatch(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer updateTmsDeliverySite(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer deleteTmsDeliverySiteItem(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer deleteTmsDeliverySiteItemCode(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);


    Integer deleteTmsDeliveryPick(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer updateTmsDeliveryOrder(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer updateTmsDistOrder(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer deleteTmsDeliveryItem(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer deleteTmsDeliveryItemCode(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

    Integer selectTmsDeliveryBatchId(@Param("beginSiteId") Integer beginSiteId,
                                     @Param("deliveryTime") String deliveryTime);
    Integer deleteTmsDeliverySite(@Param("beginSiteId") Integer beginSiteId,
                                  @Param("deliveryTime") String deliveryTime,
                                  @Param("selectTmsDeliveryBatchId") Integer selectTmsDeliveryBatchId);

    Integer updateTmsDeliverySites(@Param("beginSiteId") Integer beginSiteId,
                                   @Param("deliveryTime") String deliveryTime,
                                   @Param("selectTmsDeliveryBatchId") Integer selectTmsDeliveryBatchId);

    Integer deleteTmsDeliveryBatch(@Param("beginSiteId") Integer beginSiteId,@Param("deliveryTime") String deliveryTime);

}
