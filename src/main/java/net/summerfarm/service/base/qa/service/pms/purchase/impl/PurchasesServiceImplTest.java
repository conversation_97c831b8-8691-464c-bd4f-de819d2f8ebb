package net.summerfarm.service.base.qa.service.pms.purchase.impl;

import net.summerfarm.service.base.qa.Application;
import net.summerfarm.service.base.qa.controller.pms.purchase.input.PurchaseCreateInput;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.junit.jupiter.api.Test;

import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


@SpringBootTest(classes = Application.class)
public class PurchasesServiceImplTest {

    @Resource
    private PurchasesServiceImpl purchasesServiceImpl;



    @Test
    public void testPurchaseCreate_success() {
        // Arrange
        PurchaseCreateInput input = new PurchaseCreateInput();
//        input.setEnvFlag(1);
//        input.setSkuList(Arrays.asList("299134653764","2150225510307","2150225510305","2150336551500"));
//        input.setSupplierNo(1202);
//        input.setWarehouseNo(2);
//        input.setQuantity(10);

        // Act
        purchasesServiceImpl.purchaseCreate(input);



    }
}
