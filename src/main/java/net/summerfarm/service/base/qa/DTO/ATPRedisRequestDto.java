package net.summerfarm.service.base.qa.DTO;

import lombok.Builder;
import lombok.Data;

import java.util.concurrent.TimeUnit;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ATPRedisRequestDto
 * @date 2025-03-14
 */
@Data
@Builder
public class ATPRedisRequestDto {
    private String operation;
    private Object queryContent;
    private String queryKey;
    private long timeout;
    private TimeUnit timeUnit;
    private String dbIndex;

    public ATPRedisRequestDto() {
    }

    public ATPRedisRequestDto(String operation, Object queryContent, String queryKey, long timeout, TimeUnit timeUnit, String dbIndex) {
        this.operation = operation;
        this.queryContent = queryContent;
        this.queryKey = queryKey;
        this.timeout = timeout;
        this.timeUnit = timeUnit;
        this.dbIndex = dbIndex;
    }
}
