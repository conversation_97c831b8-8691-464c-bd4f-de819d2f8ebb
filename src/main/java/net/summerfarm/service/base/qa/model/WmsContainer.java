package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 容器表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-234 11:21:23
 */
@Getter
@Setter
@TableName("wms_container")
public class WmsContainer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 容器编码
     */
    @TableField("container_code")
    private String containerCode;

    /**
     * 容器名称
     */
    @TableField("container_name")
    private String containerName;

    /**
     * 容器属性（1：收货，2：拣货，3：库内，4：交接，5：通用）
     */
    @TableField("purpose")
    private Integer purpose;

    /**
     * 容器状态（0：禁用，1：启用）
     */
    @TableField("container_status")
    private Integer containerStatus;

    /**
     * 占用状态（0：未占用，1：已占用）
     */
    @TableField("occupy_status")
    private Integer occupyStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_operator")
    private String createOperator;

    /**
     * 更新人
     */
    @TableField("update_operator")
    private String updateOperator;

    /**
     * 长（单位：m）
     */
    @TableField("length")
    private Double length;

    /**
     * 宽（单位：m）
     */
    @TableField("width")
    private Double width;

    /**
     * 高（单位：m）
     */
    @TableField("high")
    private Double high;

    /**
     * 重量（单位：kg）
     */
    @TableField("weight")
    private Double weight;

    /**
     * 承重量（单位：kg）
     */
    @TableField("load_weight")
    private Double loadWeight;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @TableField("tenant_id")
    private Long tenantId;


}
