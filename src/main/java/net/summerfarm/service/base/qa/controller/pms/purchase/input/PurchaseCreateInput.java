package net.summerfarm.service.base.qa.controller.pms.purchase.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

@Data
public class PurchaseCreateInput{

    /**
     * 商品SKU编号，用于标识采购的商品
     */
    private List<String> skuList= Arrays.asList("858235600344");
    /**
     * 仓库编号，指定商品将被存放在哪个仓库
     */
    private Integer warehouseNo=1;
    /**
     * 采购供应商
     */
    private Integer supplierNo=1202;
    /**
     * 采购数量，表示需要采购的商品数量
     */
    private Integer quantity=10;
    /**
     * 采购标志，
     * 1.标记只生成采购单
     * 2.标记生成采购单，预约单
     * 3.标记采购到入库全流程
     */
    private Integer flag=1;
    /**
     * 备注信息，提供额外的采购说明或注释
     */
    private String remark;

    /**
     * 使用哪个环境下单的标识，qa\dev\dev2
     */
    private String envFlag= "qa";



    /**
     * 合并默认值：如果当前字段为 null 或空，则使用默认值
     * @param Input 默认值对象
     * @return 合并后的对象
     */
    public PurchaseCreateInput mergeWithDefaults(PurchaseCreateInput Input) {
        PurchaseCreateInput result = new PurchaseCreateInput();

        // List<String>
        result.skuList = (this.skuList == null || this.skuList.isEmpty()) ? Input.skuList : this.skuList;

        // Integer
        result.warehouseNo = (this.warehouseNo == null) ? Input.warehouseNo : this.warehouseNo;
        result.supplierNo = (this.supplierNo == null) ? Input.supplierNo : this.supplierNo;
        result.quantity = (this.quantity == null) ? Input.quantity : this.quantity;
        result.flag = (this.flag == null) ? Input.flag : this.flag;

        // String
        result.envFlag = (this.envFlag == null || this.envFlag.trim().isEmpty()) ? Input.envFlag : this.envFlag;

        // 允许 remark 为空
        result.remark = this.remark;

        return result;
    }
}
