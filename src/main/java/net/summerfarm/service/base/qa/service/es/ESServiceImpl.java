package net.summerfarm.service.base.qa.service.es;

import net.summerfarm.service.base.qa.DTO.ESCaseDto;
import net.summerfarm.service.base.qa.model.AICaseES;
import net.summerfarm.service.base.qa.model.AICaseEdit;
import net.summerfarm.service.base.qa.model.AICaseExport;
import net.summerfarm.service.base.qa.model.AIPoint2case;
import net.summerfarm.service.base.qa.model.esRespo.AIEditRepository;
import net.summerfarm.service.base.qa.model.esRespo.AIExportRepository;
import net.summerfarm.service.base.qa.model.esRespo.AIPoint2caseRepository;
import net.summerfarm.service.base.qa.model.esRespo.TestPointRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName ESServiceImpl
 * @date 2025-04-15
 */
@Service
public class ESServiceImpl {

    private final TestPointRepository testPointRepository;

    public ESServiceImpl(TestPointRepository testPointRepository) {
        this.testPointRepository = testPointRepository;
    }

    @Resource
    private AIEditRepository aiEditRepository;

    @Resource
    private AIExportRepository aiExportRepository;

    @Resource
    private AIPoint2caseRepository aiPoint2caseRepository;


    /**
     * 将测试点数据保存到Elasticsearch中。
     *
     * @param key          主键
     * @param projectId2   项目ID
     * @param prompt       用户输入的提示内容
     * @param response     AI生成的响应内容
     * @param operateTime  操作时间（ISO 8601格式）
     * @param fileName     相关文件名
     */
    public void insertToES(String key, String projectId2, String prompt, String response, String operateTime, String fileName) {
        testPointRepository.save(new AICaseES(key, projectId2, prompt, response, operateTime, fileName));
    }

    /**
     * 将AI案例编辑记录保存到Elasticsearch。
     *
     * @param projectId    项目ID
     * @param operation    操作类型（如修改、删除等）
     * @param subProjectId 子项目ID
     * @param originalData 原始数据内容
     * @param operateTime  操作时间（ISO 8601格式）
     */
    public void editInsertToES(String projectId, String operation, String subProjectId, String originalData, String operateTime) {
        aiEditRepository.save(new AICaseEdit(projectId, operation, subProjectId, originalData, operateTime));
    }

    /**
     * 将测试点生成的测试用例记录保存到ES。
     *
     * @param projectId        项目ID
     * @param determinedTestPoint 确定的测试点数据（对象）
     * @param operateTime      操作时间（ISO 8601格式）
     * @param generatedCase    生成的测试用例数据（对象）
     */
    public void point2caseInsertToES(String projectId, Object determinedTestPoint, String operateTime, Object generatedCase) {
        aiPoint2caseRepository.save(new AIPoint2case(projectId, determinedTestPoint, operateTime, generatedCase));
    }

    /**
     * 将导出的测试用例记录保存到ES。
     *
     * @param projectId        项目ID
     * @param generatedCase    生成的测试用例数据（对象）
     * @param selectedTestPoints 选中的测试点标识（如JSON数组字符串）
     * @param operateTime      操作时间（ISO 8601格式）
     */
    public void exportInsertToES(String projectId, Object generatedCase, String selectedTestPoints, String operateTime) {
        aiExportRepository.save(new AICaseExport(projectId, generatedCase, selectedTestPoints, operateTime));
    }

    public List<AICaseES> findByProjectId(String projectId) {
        return testPointRepository.findByProjectId(projectId);
    }


}
