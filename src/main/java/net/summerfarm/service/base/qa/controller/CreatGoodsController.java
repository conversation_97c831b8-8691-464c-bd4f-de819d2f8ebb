package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.goods.CreateNewGoods;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/goods")
    public class CreatGoodsController {
    @Resource
    CreateNewGoods createNewGoods;
    @GetMapping(value = "/goodsCreate")
    public String CreatXmGoods( @RequestParam(value = "skuName", required = false)String skuName,
            @RequestParam(value = "skuType", required = false)String skuType,
            @RequestParam(value = "area", required = false)String area)throws Exception{
        return createNewGoods.NewGoods(skuName,skuType,area);

    }

}
