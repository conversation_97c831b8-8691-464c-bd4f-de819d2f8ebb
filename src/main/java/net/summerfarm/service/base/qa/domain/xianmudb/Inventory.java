package net.summerfarm.service.base.qa.domain.xianmudb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "商品SKU实体类")
public class Inventory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "inv_id", type = IdType.AUTO)
    private Long invId;

    /**
     * 产品编号
     */
    @TableField("sku")
    private String sku;

    /**
     * 仓库(实际)库存
     */
    @TableField("store_quantity")
    private Integer storeQuantity;

    /**
     * 限制购买数量
     */
    @TableField("limited_quantity")
    private Integer limitedQuantity;

    /**
     * 属性ID
     */
    @TableField("ait_id")
    private Integer aitId;

    @TableField("pd_id")
    private Long pdId;

    /**
     * 产地
     */
    @TableField("origin")
    private String origin;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 生熟度
     */
    @TableField("maturity")
    private String maturity;

    /**
     * 包数
     */
    @TableField("pack")
    private String pack;

    /**
     * 重量
     */
    @TableField("weight")
    private String weight;

    /**
     * 生产日期
     */
    @TableField("production_date")
    private LocalDate productionDate;

    /**
     * 贮存方式(作废)
     */
    @TableField("storage_method")
    private String storageMethod;

    /**
     * 广告语
     */
    @TableField("slogan")
    private String slogan;

    /**
     * 销售量
     */
    @TableField("sale_count")
    private Integer saleCount;

    /**
     * 销售模式；0普通，1团购，2日限购，3永久限购，4新手限购，5订单限购，6定单价函数限购
     */
    @TableField("sales_mode")
    private Integer salesMode;

    /**
     * 警戒库存
     */
    @TableField("alert_inventory")
    private Integer alertInventory;

    /**
     * 安全库存
     */
    @TableField("safe_inventory")
    private Integer safeInventory;

    /**
     * 销售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 促销价
     */
    @TableField("promotion_price")
    private BigDecimal promotionPrice;

    /**
     * 商品介绍
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 售后最大数量
     */
    @TableField("after_sale_quantity")
    private Integer afterSaleQuantity;

    /**
     * SKU生命周期：-1、上新处理中 0、使用中 1、已删除
     */
    @TableField("outdated")
    private Integer outdated;

    /**
     * 最小起售量
     */
    @TableField("base_sale_quantity")
    private Integer baseSaleQuantity;

    /**
     * 售卖规格
     */
    @TableField("base_sale_unit")
    private Integer baseSaleUnit;

    /**
     * 体积(长*宽*高)
     */
    @TableField("volume")
    private String volume;

    /**
     * 重量kg
     */
    @TableField("weight_num")
    private BigDecimal weightNum;

    /**
     * 类型 0 自营 1 代仓
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 所属大客户ID
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     *  0 不加入 1加入
     */
    @TableField("sample_pool")
    private Integer samplePool;

    /**
     * sku图
     */
    @TableField("sku_pic")
    private String skuPic;

    /**
     * 售后单位
     */
    @TableField("after_sale_unit")
    private String afterSaleUnit;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 上新审核状态：0、待上新 1、上新成功 2、上新失败
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 创建人adminId
     */
    @TableField("creator")
    private Integer creator;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    @TableField("ext_type")
    private Integer extType;

    @TableField("create_remark")
    private String createRemark;

    /**
     * 任务类型：0、SPU 1、SKU
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 上新类型：0、平台 1、大客户 2、saas自营&代仓 3、仅saas自营
     */
    @TableField("create_type")
    private Integer createType;

    /**
     * 操作人adminId
     */
    @TableField("auditor")
    private Integer auditor;

    /**
     * 规格备注
     */
    @TableField("weight_notes")
    private String weightNotes;

    /**
     * 是否为国产，0：不是，1是
     */
    @TableField("is_domestic")
    private Integer isDomestic;

    /**
     * 供应商是否可见：0不可见，1可见
     */
    @TableField("supplier_visible")
    private Integer supplierVisible;

    /**
     * 0 不展示平均价  1 展示平均价
     */
    @TableField("average_price_flag")
    private Integer averagePriceFlag;

    /**
     * sku名称
     */
    @TableField("sku_name")
    private String skuName;

    /**
     * 拒绝原因
     */
    @TableField("refuse_reason")
    private String refuseReason;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    @TableField("sub_type")
    private Integer subType;


}
