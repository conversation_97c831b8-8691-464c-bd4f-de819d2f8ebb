//package net.summerfarm.service.base.qa.utils;
//
//import org.springframework.stereotype.Component;
//
//import java.util.Collections;
//
///**
// * 类<code>Doc</code>用于：
// * dubbo配置实例
// * <AUTHOR>
// * @ClassName DubboConfig
// * @date 2025-02-07
// */
//
//@Component
//public class DubboConfig {
//
//
////    @Value("${nacos.config.server-addr}")
////    private String serverAddr;
////
////    @Value("${nacos.config.namespace}")
////    private String namespace;
//
//    public ReferenceConfig<GenericService> referenceConfig(String interfaceName){
//        RegistryConfig registry = new RegistryConfig();
//        registry.setAddress("serverAddr");
//        registry.setProtocol("nacos");
//        registry.setParameters(Collections.singletonMap("namespace", "namespace"));
//        ApplicationConfig application = new ApplicationConfig();
//        application.setName("ATP");
//        application.setRegistry(registry);
//        ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
//        reference.setApplication(application);
//        reference.setInterface(interfaceName); // 替换为实际的服务接口名
//        reference.setGeneric(true); // 声明为泛化接口
//        reference.setVersion("1.0.0");
//        reference.setGroup("online");
//        reference.getServices();
//
//        // 获取泛化服务实例
//
//        return reference;
//    }
//}
