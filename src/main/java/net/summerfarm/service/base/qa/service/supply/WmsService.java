package net.summerfarm.service.base.qa.service.supply;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.mapper.ofcdb.FulfillmentItemMapper;
import net.summerfarm.service.base.qa.mapper.ofcdb.FulfillmentOrderMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WarehouseLogisticsCenterMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsStockTaskNoticeOrderMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsStockTaskWaveConfigMapper;
import net.summerfarm.service.base.qa.model.*;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Service
public class WmsService {
    @Resource
    private FulfillmentOrderMapper fulfillmentOrderMapper;
    @Resource
    private FulfillmentItemMapper fulfillmentItemMapper;
    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);
    @Resource
    private ConfigValue configValue;
    @Resource
    private WmsStockTaskWaveConfigMapper wmsStockTaskWaveConfigMapper;
    @Resource
    private TokenService tokenService;
    @Resource
    private WmsStockTaskNoticeOrderMapper wmsStockTaskNoticeOrderMapper;
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;
    public String wmsDeliver(String orderNo,String isWave) {
        if (orderNo.isEmpty()){
            return "请输入订单号";
        }
        Long taskId = null;
        Map<String, String> headers = new HashMap<>();
        LambdaQueryWrapper<FulfillmentOrder> ofcOrderWrapper = new LambdaQueryWrapper<>();
        ofcOrderWrapper.eq(FulfillmentOrder::getOutOrderNo, orderNo);
        List<FulfillmentOrder> ofcOrderDb = fulfillmentOrderMapper.selectList(ofcOrderWrapper);
        if (ofcOrderDb.isEmpty()) {
            return "对应履约单不存在";
        }

        Integer fulfillmentType = ofcOrderDb.get(0).getFulfillmentType();
        Long fulfillmentNo = ofcOrderDb.get(0).getFulfillmentNo();
        LocalDate fulfillmentTime = ofcOrderDb.get(0).getFulfillmentTime();

        LambdaQueryWrapper<FulfillmentItem> ofcItemWrapper = new LambdaQueryWrapper<>();
        ofcItemWrapper.eq(FulfillmentItem::getFulfillmentNo, fulfillmentNo);
        List<FulfillmentItem> ofcItemDb = fulfillmentItemMapper.selectList(ofcItemWrapper);
        Integer skuSubType = ofcItemDb.get(0).getSkuSubType();
        Integer warehouseNo = ofcItemDb.get(0).getWarehouseNo();
        if (isWave.equals("1") &&(skuSubType==5 || skuSubType==1)){
            return "POP 和 代销不入仓不支持波次";
        }
        if (skuSubType == 5) {
            String taskBody = "{\"JobId\":24901296,\n" +
                    "\"Namespace\":\"qa\",\n" +
                    "\"InstanceParameters\":\"" + fulfillmentTime + "\",\n" +
                    "\"GroupId\":\"summerfarm-ofc\"}";
            String taskResponse = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody, headers);
            System.out.println(taskResponse);

            //POP订单转采
        }
        LocalDate currentDate = LocalDate.now();
        LocalDate nextDay = currentDate.plusDays(1);
        LocalDateTime nextDayMidnight = LocalDateTime.of(nextDay, LocalTime.MIDNIGHT);
        LocalDate previousDate = fulfillmentTime.minusDays(1);
        LambdaQueryWrapper<WmsStockTaskNoticeOrder> noticeOrderWrapper = new LambdaQueryWrapper<>();
        noticeOrderWrapper.eq(WmsStockTaskNoticeOrder::getOutOrderNo, orderNo);

        if (skuSubType == 1) {
            //代销不入仓订单转采
            long daysBetween = ChronoUnit.DAYS.between(currentDate, fulfillmentTime);
            if (daysBetween > 2) {
                return "代销不入仓的订单提货日期大于2的转采只能转成预提";
            }
            String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
            headers.put("Token", token);
            String saleToPurchase = "{\n" +
                    "    \"pickupDate\": \"" + previousDate + "\",\n" +
                    "    \"warehouseNo\": " + warehouseNo + " \n" +
                    "}";
            String saleToPurchaseResponse = okHttpUtils.sendCustomJsonPost("https://devadmin.summerfarm.net/summerfarm-ofc/saleToPurchase/debug", saleToPurchase, headers);
            System.out.println(saleToPurchaseResponse);
            for (int i = 5; i > 0; i++) {
                try {
                    // 使用 TimeUnit 让线程睡眠2秒
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                noticeOrderWrapper
                        .eq(WmsStockTaskNoticeOrder::getOutOrderNo, orderNo)
                        .ne(WmsStockTaskNoticeOrder::getStatus,40)
                        .orderByDesc(WmsStockTaskNoticeOrder::getId);
                List<WmsStockTaskNoticeOrder> noticeOrderDbs = wmsStockTaskNoticeOrderMapper.selectList(noticeOrderWrapper);
                if (!noticeOrderDbs.isEmpty()) {
                    break;
                }
            }
        }
            //修改出库通知单的配送时间，获取出库通知单的类型看是那种类型
            List<WmsStockTaskNoticeOrder> noticeOrderDb = wmsStockTaskNoticeOrderMapper.selectList(noticeOrderWrapper);
            if (noticeOrderDb.isEmpty()){
                return "未查到 出库通知单，请检查订单是否异常";
            }
            Integer storeNo = noticeOrderDb.get(0).getStoreNo();
            WmsStockTaskNoticeOrder entityNoticeOrder = new WmsStockTaskNoticeOrder();
            entityNoticeOrder.setExceptTime(nextDayMidnight);
            wmsStockTaskNoticeOrderMapper.update(entityNoticeOrder, noticeOrderWrapper);
            Integer noticeSkuFlag = noticeOrderDb.get(0).getNoticeSkuFlag();
            Integer supplyMode = noticeOrderDb.get(0).getSupplyMode();
            if (noticeSkuFlag == 4) {
                for (int i = 5; i > 0; i++) {
                    try {
                        // 使用 TimeUnit 让线程睡眠2秒
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    noticeOrderWrapper.eq(WmsStockTaskNoticeOrder::getOutOrderNo, orderNo);
                    List<WmsStockTaskNoticeOrder> noticeOrderDbs = wmsStockTaskNoticeOrderMapper.selectList(noticeOrderWrapper);
                    if (!noticeOrderDbs.isEmpty()) {
                        break;
                    }
                }

                String taskBody = "{\"JobId\":24901575,\n" +
                        "\"Namespace\":\"qa\",\n" +
                        "\"InstanceParameters\":\"" + fulfillmentTime + "\",\n" +
                        "\"GroupId\":\"summerfarm-wms\"}";
                String taskResponse = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody, headers);
                System.out.println(taskResponse);
                //POP出库任务
            }
            //获取截单时间
            LambdaQueryWrapper<WarehouseLogisticsCenter> logisticsWrapper = new LambdaQueryWrapper<>();
            logisticsWrapper.eq(WarehouseLogisticsCenter::getStoreNo, storeNo);
            List<WarehouseLogisticsCenter> logisticsDb = warehouseLogisticsCenterMapper.selectList(logisticsWrapper);
            String closeTime = logisticsDb.get(0).getCloseTime();
            WarehouseLogisticsCenter entityLogisticsCenter = new WarehouseLogisticsCenter();
            entityLogisticsCenter.setCloseTime("22:00:00");
            warehouseLogisticsCenterMapper.update(entityLogisticsCenter, logisticsWrapper);
            if (noticeSkuFlag == 2 && supplyMode == 1) {
                String taskBody = "{\"JobId\":8741573,\n" +
                        "\"Namespace\":\"qa\",\n" +
                        "\"GroupId\":\"summerfarm-wms\"}";
                String taskBody2 = "{\"JobId\":29286824,\n" +
                        "\"Namespace\":\"qa\",\n" +
                        "\"GroupId\":\"summerfarm-wms\"}";
                String taskResponse = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody, headers);
                System.out.println(taskResponse);
                String taskResponse2 = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody2, headers);
                System.out.println(taskResponse2);

                //越库类型的出库任务
            }
            else {
                if (isWave.equals("1")){
                    Date now = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
                    String currentTime = sdf.format(now);
                    LambdaQueryWrapper<WmsStockTaskWaveConfig> waveWrapper = new LambdaQueryWrapper<>();
                    waveWrapper.eq(WmsStockTaskWaveConfig::getStoreNo, storeNo);
                    WmsStockTaskWaveConfig entityWave= new WmsStockTaskWaveConfig();
                    entityWave.setWaveTime(currentTime);
                    wmsStockTaskWaveConfigMapper.update(entityWave, waveWrapper);
                    try {
                        // 使用 TimeUnit 让线程睡眠2秒
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }//过2S再执行
                    String taskBody = "{\"JobId\":8741582,\n" +
                            "\"Namespace\":\"qa\",\n" +
                            "\"GroupId\":\"summerfarm-wms\"}";
                    String taskResponse = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody, headers);
                    System.out.println(taskResponse);

                }
                else {
                    String taskBody = "{\"JobId\":8370751,\n" +
                            "\"Namespace\":\"qa\",\n" +
                            "\"GroupId\":\"summerfarm-wms\"}";
                    String taskBody2 = "{\"JobId\":29286825,\n" +
                            "\"Namespace\":\"qa\",\n" +
                            "\"GroupId\":\"summerfarm-wms\"}";
                    String taskResponse = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody, headers);
                    System.out.println(taskResponse);
                    String taskResponse2 = okHttpUtils.sendCustomJsonPost("https://test-tool.summerfarm.net/run/execute/run", taskBody2, headers);
                    System.out.println(taskResponse2);
                }
                //其他类型统一为截单出库任务生成
            }

            for (int i = 5; i > 0; i--) {
                try {
                    // 使用 TimeUnit 让线程睡眠2秒
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                noticeOrderWrapper
                        .eq(WmsStockTaskNoticeOrder::getOutOrderNo, orderNo)
                        .ne(WmsStockTaskNoticeOrder::getStatus,40)
                        .orderByDesc(WmsStockTaskNoticeOrder::getId);
                List<WmsStockTaskNoticeOrder> noticeOrderDbs = wmsStockTaskNoticeOrderMapper.selectList(noticeOrderWrapper);

                taskId = noticeOrderDbs.get(0).getStockTaskId();
                if (taskId != null) {
                     break;
                }
            }
            WarehouseLogisticsCenter entityLogisticsCenter2 = new WarehouseLogisticsCenter();
            entityLogisticsCenter2.setCloseTime(closeTime);
            warehouseLogisticsCenterMapper.update(entityLogisticsCenter2, logisticsWrapper);

        if (taskId != null) {
            return taskId.toString();
        } else {
            return "出库任务未生成，请重试一遍";
        }
    }
}
