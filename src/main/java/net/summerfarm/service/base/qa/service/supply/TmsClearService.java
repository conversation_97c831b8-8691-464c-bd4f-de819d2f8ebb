package net.summerfarm.service.base.qa.service.supply;

import net.summerfarm.service.base.qa.mapper.xianmudb.TmsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TmsClearService {
    @Resource
    private TmsMapper tmsMapper;
    public String clearCutoff(String deliveryNo,String deliveryTime)throws Exception{
        Integer beginSiteId = tmsMapper.beginSiteId(deliveryNo);
        Integer updateTmsDeliveryBatch = tmsMapper.updateTmsDeliveryBatch(beginSiteId,deliveryTime);
        Integer updateTmsDeliverySite = tmsMapper.updateTmsDeliverySite(beginSiteId,deliveryTime);
        Integer deleteTmsDeliverySiteItem = tmsMapper.deleteTmsDeliverySiteItem(beginSiteId,deliveryTime);
        Integer deleteTmsDeliverySiteItemCode =tmsMapper.deleteTmsDeliverySiteItemCode(beginSiteId,deliveryTime);
        Integer deleteTmsDeliveryPick = tmsMapper.deleteTmsDeliveryPick(beginSiteId,deliveryTime);
        Integer updateTmsDeliveryOrder = tmsMapper.updateTmsDeliveryOrder(beginSiteId,deliveryTime);
        Integer updateTmsDistOrder=  tmsMapper.updateTmsDistOrder(beginSiteId,deliveryTime);
        Integer deleteTmsDeliveryItem=  tmsMapper.deleteTmsDeliveryItem(beginSiteId,deliveryTime);
        Integer deleteTmsDeliveryItemCode= tmsMapper.deleteTmsDeliveryItemCode(beginSiteId,deliveryTime);
        Integer selectTmsDeliveryBatchId =  tmsMapper.selectTmsDeliveryBatchId(beginSiteId,deliveryTime);
        Integer deleteTmsDeliverySite =tmsMapper.deleteTmsDeliverySite(beginSiteId,deliveryTime,selectTmsDeliveryBatchId);
        Integer updateTmsDeliverySites =tmsMapper.updateTmsDeliverySites(beginSiteId,deliveryTime,selectTmsDeliveryBatchId);
        Integer deleteTmsDeliveryBatch =tmsMapper.deleteTmsDeliveryBatch(beginSiteId,deliveryTime);

        return "ture";
    }
}
