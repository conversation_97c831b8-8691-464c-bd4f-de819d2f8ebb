package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AICase
 * @date 2025-04-18
 */
@Data
@TableName("ai_case")
public class AICase {

    private long id;
    private String projectId;
    private String phase;
    private String fileName;
    private long status;
    private long pointAcceptanceRate;
    private long caseAcceptanceRate;
    private LocalDateTime createTime;
    private int flag;
    private String review;
}
