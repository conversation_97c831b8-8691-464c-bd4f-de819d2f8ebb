package net.summerfarm.service.base.qa.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.domain.PmsSupplyList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【pms_supply_list(供货目录（品仓供应商信息）)】的数据库操作Mapper
* @createDate 2025-06-10 14:49:07
* @Entity net.summerfarm.service.base.qa.domain.PmsSupplyList
*/
@DS("XIANMU_DB")
public interface PmsSupplyListMapper extends BaseMapper<PmsSupplyList> {

}




