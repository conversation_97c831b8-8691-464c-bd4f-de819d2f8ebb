package net.summerfarm.service.base.qa.service.goods.node;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.domain.xianmudb.Inventory;
import net.summerfarm.service.base.qa.domain.xianmudb.Products;
import net.summerfarm.service.base.qa.mapper.xianmudb.InventoryMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.ProductsMapper;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.service.goods.model.req.goodscreate.GoodsCreateParam;
import net.summerfarm.service.base.qa.service.goods.model.req.goodscreate.Sku;
import net.summerfarm.service.base.qa.service.goods.model.res.GoodsCreateRes;
import net.summerfarm.service.base.qa.utils.DbChecker;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class GoodsNode {

    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);
    private static String defaultBody = "{\n" +
            "    \"pdName\": \"自动下单商品\",\n" +
            "    \"pddetail\": \"自动下单商品副标题\",\n" +
            "    \"otherSlogan\": \"图片中是很好看的商品图\",\n" +
            "    \"afterSaleTime\": 48,\n" +
            "    \"warnTime\": 309,\n" +
            "    \"qualityTime\": 199,\n" +
            "    \"qualityTimeUnit\": \"day\",\n" +
            "    \"qualityTimeType\": 0,\n" +
            "    \"refundType\": \"拍多/拍错/不想要;缺货;其他\",\n" +
            "    \"realName\": \"自动下单商品自动化实物名称\",\n" +
            "    \"afterSaleType\": \"商品数量不符;包装问题;商品品质问题;保质期不符;平台发错货;其他\",\n" +
            "    \"storageLocation\": 1,\n" +
            "    \"afterSaleUnit\": \"\",\n" +
            "    \"auditStatus\": 1,\n" +
            "    \"createStatus\": 1,\n" +
            "    \"createType\": \"\",\n" +
            "    \"commentPic\": [],\n" +
            "    \"productIntroduction\": \"\",\n" +
            "    \"picturePath\": \"test/tpoxrx2lhpc1gfad2.jpg\",\n" +
            "    \"detailPicture\": \"test/kj6oafqpeps1gfdnq.jpg,test/o9xq3j09okd1gfgur.jpg,test/e83k5q2757h1gft6m.jpg\",\n" +
            "    \"categoryId\": 2160,\n" +
            "    \"keyValueList\": [\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatType\": 3,\n" +
            "            \"id\": 1,\n" +
            "            \"name\": \"产地\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"formatStr\": [],\n" +
            "            \"value\": \"浙江\",\n" +
            "            \"productsPropertyId\": 1,\n" +
            "            \"productsPropertyValue\": \"浙江\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatType\": 3,\n" +
            "            \"id\": 2,\n" +
            "            \"name\": \"品牌\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"formatStr\": [],\n" +
            "            \"value\": \"深海鱼\",\n" +
            "            \"productsPropertyId\": 2,\n" +
            "            \"productsPropertyValue\": \"深海鱼\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatStr\": [\n" +
            "                \"冷藏\",\n" +
            "                \"冷冻\",\n" +
            "                \"常温\"\n" +
            "            ],\n" +
            "            \"formatType\": 2,\n" +
            "            \"id\": 4,\n" +
            "            \"name\": \"储藏区域\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"value\": \"冷藏\",\n" +
            "            \"productsPropertyId\": 4,\n" +
            "            \"productsPropertyValue\": \"冷藏\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatType\": 3,\n" +
            "            \"id\": 5,\n" +
            "            \"name\": \"储藏温度\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"formatStr\": [],\n" +
            "            \"value\": \"30度\",\n" +
            "            \"productsPropertyId\": 5,\n" +
            "            \"productsPropertyValue\": \"30度\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatType\": 3,\n" +
            "            \"id\": 17,\n" +
            "            \"name\": \"使用方法\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"formatStr\": [],\n" +
            "            \"value\": \"高温烹炸\",\n" +
            "            \"productsPropertyId\": 17,\n" +
            "            \"productsPropertyValue\": \"高温烹炸\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "            \"creator\": \"初始化\",\n" +
            "            \"formatType\": 3,\n" +
            "            \"id\": 18,\n" +
            "            \"name\": \"成分\",\n" +
            "            \"status\": 1,\n" +
            "            \"type\": 0,\n" +
            "            \"formatStr\": [],\n" +
            "            \"value\": \"鱼肉\",\n" +
            "            \"productsPropertyId\": 18,\n" +
            "            \"productsPropertyValue\": \"鱼肉\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"salePropertyList\": [\n" +
            "        {\n" +
            "            \"id\": 7,\n" +
            "            \"name\": \"规格\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 21,\n" +
            "            \"name\": \"口味\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"id\": 27,\n" +
            "            \"name\": \"尺寸\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"createRemark\": \"{\\\"commentPic\\\":[]}\",\n" +
            "    \"skuList\": [\n" +
            "        {\n" +
            "            \"sku\": \"新增SKU\",\n" +
            "            \"weight\": \"(深海鱼油销售说明)\",\n" +
            "            \"type\": 0,\n" +
            "            \"subType\": 3,\n" +
            "            \"SubTypeArray\": [\n" +
            "                \"0\",\n" +
            "                \"3\"\n" +
            "            ],\n" +
            "            \"adminId\": \"\",\n" +
            "            \"extType\": 0,\n" +
            "            \"averagePriceFlag\": 0,\n" +
            "            \"unit\": \"盒\",\n" +
            "            \"origin\": \"\",\n" +
            "            \"maturity\": \"\",\n" +
            "            \"afterSaleQuantity\": 15,\n" +
            "            \"costPrice\": 0,\n" +
            "            \"marketPrice\": 0,\n" +
            "            \"show\": true,\n" +
            "            \"limitedQuantity\": 0,\n" +
            "            \"areaSkuVOS\": [],\n" +
            "            \"info\": \"\",\n" +
            "            \"baseSaleUnit\": 1,\n" +
            "            \"baseSaleQuantity\": 1,\n" +
            "            \"skuPic\": \"\",\n" +
            "            \"saleValueList\": [\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-01 17:06:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatStr\": [\n" +
            "                        \"mL\",\n" +
            "                        \"L\",\n" +
            "                        \"G\",\n" +
            "                        \"KG\",\n" +
            "                        \"个\",\n" +
            "                        \"斤\",\n" +
            "                        \"箱\",\n" +
            "                        \"盒\",\n" +
            "                        \"包\",\n" +
            "                        \"袋\",\n" +
            "                        \"瓶\",\n" +
            "                        \"罐\",\n" +
            "                        \"桶\",\n" +
            "                        \"卷\",\n" +
            "                        \"块\",\n" +
            "                        \"片\",\n" +
            "                        \"颗\",\n" +
            "                        \"支\",\n" +
            "                        \"条\",\n" +
            "                        \"只\",\n" +
            "                        \"张\",\n" +
            "                        \"套\",\n" +
            "                        \"组\"\n" +
            "                    ],\n" +
            "                    \"formatType\": 0,\n" +
            "                    \"id\": 7,\n" +
            "                    \"name\": \"规格\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"productsPropertyValue\": \"1KG*5KG\",\n" +
            "                    \"weight\": \"容量*数量\",\n" +
            "                    \"weightType\": \"净重\",\n" +
            "                    \"firstUnit\": \"KG\",\n" +
            "                    \"firstWeight\": 1,\n" +
            "                    \"secondWeight\": 5,\n" +
            "                    \"secondUnit\": \"KG\",\n" +
            "                    \"productsPropertyId\": 7\n" +
            "                },\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatType\": 3,\n" +
            "                    \"id\": 21,\n" +
            "                    \"name\": \"口味\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"formatStr\": [],\n" +
            "                    \"value\": \"爽口\",\n" +
            "                    \"productsPropertyId\": 21,\n" +
            "                    \"productsPropertyValue\": \"爽口\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-04 17:06:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatType\": 3,\n" +
            "                    \"id\": 27,\n" +
            "                    \"name\": \"尺寸\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"formatStr\": [],\n" +
            "                    \"value\": \"2米\",\n" +
            "                    \"productsPropertyId\": 27,\n" +
            "                    \"productsPropertyValue\": \"2米\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"createStatus\": \"\",\n" +
            "            \"commentPic\": \"\",\n" +
            "            \"commentInfo\": \"\",\n" +
            "            \"commentStore\": \"\",\n" +
            "            \"commentPrice\": \"\",\n" +
            "            \"createType\": 0,\n" +
            "            \"afterSaleUnit\": \"条\",\n" +
            "            \"samplePool\": 1,\n" +
            "            \"isDomestic\": 1,\n" +
            "            \"productLabelValueVos\": [\n" +
            "                {\n" +
            "                    \"labelField\": \"replenishment_plan\",\n" +
            "                    \"labelId\": 1001,\n" +
            "                    \"labelName\": \"参与补货计划\",\n" +
            "                    \"labelValue\": 0\n" +
            "                },\n" +
            "                {\n" +
            "                    \"labelField\": \"supplier_visible\",\n" +
            "                    \"labelId\": 1002,\n" +
            "                    \"labelName\": \"供应商可见\",\n" +
            "                    \"labelValue\": 0\n" +
            "                },\n" +
            "                {\n" +
            "                    \"labelField\": \"consignment_sku\",\n" +
            "                    \"labelId\": 1003,\n" +
            "                    \"labelName\": \"SKU代售\",\n" +
            "                    \"labelValue\": 0\n" +
            "                }\n" +
            "            ],\n" +
            "            \"role\": true,\n" +
            "            \"skuName\": \"自动化sku名称\",\n" +
            "            \"weightNum\": 3.5,\n" +
            "            \"saleValueData\": [\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-01 17:06:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatStr\": [\n" +
            "                        \"mL\",\n" +
            "                        \"L\",\n" +
            "                        \"G\",\n" +
            "                        \"KG\",\n" +
            "                        \"个\",\n" +
            "                        \"斤\",\n" +
            "                        \"箱\",\n" +
            "                        \"盒\",\n" +
            "                        \"包\",\n" +
            "                        \"袋\",\n" +
            "                        \"瓶\",\n" +
            "                        \"罐\",\n" +
            "                        \"桶\",\n" +
            "                        \"卷\",\n" +
            "                        \"块\",\n" +
            "                        \"片\",\n" +
            "                        \"颗\",\n" +
            "                        \"支\",\n" +
            "                        \"条\",\n" +
            "                        \"只\",\n" +
            "                        \"张\",\n" +
            "                        \"套\",\n" +
            "                        \"组\"\n" +
            "                    ],\n" +
            "                    \"formatType\": 0,\n" +
            "                    \"id\": 7,\n" +
            "                    \"name\": \"规格\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"productsPropertyValue\": \"1KG*5KG\",\n" +
            "                    \"weight\": \"容量*数量\",\n" +
            "                    \"weightType\": \"净重\",\n" +
            "                    \"firstUnit\": \"KG\",\n" +
            "                    \"firstWeight\": 1,\n" +
            "                    \"secondWeight\": 5,\n" +
            "                    \"secondUnit\": \"KG\",\n" +
            "                    \"productsPropertyId\": 7\n" +
            "                },\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-11 17:07:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatType\": 3,\n" +
            "                    \"id\": 21,\n" +
            "                    \"name\": \"口味\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"formatStr\": [],\n" +
            "                    \"value\": \"爽口\",\n" +
            "                    \"productsPropertyId\": 21,\n" +
            "                    \"productsPropertyValue\": \"爽口\"\n" +
            "                },\n" +
            "                {\n" +
            "                    \"createTime\": \"2020-05-04 17:06:21\",\n" +
            "                    \"creator\": \"初始化\",\n" +
            "                    \"formatType\": 3,\n" +
            "                    \"id\": 27,\n" +
            "                    \"name\": \"尺寸\",\n" +
            "                    \"status\": 1,\n" +
            "                    \"type\": 1,\n" +
            "                    \"show\": true,\n" +
            "                    \"formatStr\": [],\n" +
            "                    \"value\": \"2米\",\n" +
            "                    \"productsPropertyId\": 27,\n" +
            "                    \"productsPropertyValue\": \"2米\"\n" +
            "                }\n" +
            "            ],\n" +
            "            \"volume\": \"0.150*0.160*0.200\",\n" +
            "            \"msg\": \"深海鱼油销售说明\",\n" +
            "            \"auditFlag\": \"\",\n" +
            "            \"createRemark\": \"{\\\"commentPic\\\":\\\"\\\",\\\"commentInfo\\\":\\\"\\\",\\\"commentStore\\\":\\\"\\\",\\\"commentPrice\\\":\\\"\\\",\\\"createType\\\":0}\",\n" +
            "            \"isNew\": true\n" +

            "        }\n" +
            "    ]\n" +
            "}";
    @Resource
    TokenService tokenService;

    @Resource
    ConfigValue configValue;

    @Resource
    ProductsMapper productsMapper;

    @Resource
    InventoryMapper inventoryMapper;

    public String goodsCreate(String goodsInfo){
        if (StringUtils.isEmpty(goodsInfo)){
            goodsInfo = defaultBody;
        }
        GoodsCreateParam goodsCreateParam = JSON.parseObject(goodsInfo, GoodsCreateParam.class);
        goodsCreateParam.setPdName(goodsCreateParam.getPdName() + System.currentTimeMillis());
        goodsInfo = JSON.toJSONString(goodsCreateParam);
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String result = okHttpUtils.sendCustomJsonPut(configValue.summerfarmManageUri + "/products/operate", goodsInfo, headers);
        GoodsCreateRes data = JSON.parseObject(JSON.parseObject(result).getString("data"), GoodsCreateRes.class);
        Map<String, String> goodsCreateResult = data.getOuterApplicationMap();
        List<String> checkResult = new ArrayList<>();
        //获取商品id
        //products 商品主表校验
        productCheck(goodsCreateResult, goodsCreateParam, checkResult);

        //inventory              sku信息表校验


        //products_property_mapping    商品属性关系表校验

        //products_property          商品属性项表校验

        //products_property_value     商品属性值表校验

        //tax_rate_config           税率信息表校验

        //product_label_value     商品标签表校验

        return JSON.toJSONString(data);
    }

    private void inventory(Map<String, String> goodsCreateResult, GoodsCreateParam goodsCreateParam, List<String> checkResult){
        String pdId = goodsCreateResult.get("application:null");
        LambdaQueryWrapper<Inventory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Inventory::getPdId, pdId);
        List<Inventory> inventoriesDb = inventoryMapper.selectList(lambdaQueryWrapper);
        List<Sku> skuList = goodsCreateParam.getSkuList();
        inventoriesDb.forEach(inventory -> {
            skuList.forEach(sku -> {
//                if (inventory.getSkuName().equals())
            });
        });
    }

    /**
     * 商品主表校验
     * @param goodsCreateResult
     * @param goodsCreateParam
     * @param checkResult
     */
    private void productCheck(Map<String, String> goodsCreateResult, GoodsCreateParam goodsCreateParam, List<String> checkResult){
        String pdId = goodsCreateResult.get("application:null");
        String pdNo = goodsCreateResult.get("applicationSpuCode:null");
        Products productsDb = productsMapper.selectById(pdId);
        DbChecker.checker(productsDb.getCategoryId(), goodsCreateParam.getCategoryId(), "category_id");
        DbChecker.checker(productsDb.getPdName(), goodsCreateParam.getPdName(), "category_id");
        DbChecker.checker(productsDb.getPddetail(), goodsCreateParam.getPddetail(), "category_id");
        DbChecker.checker(productsDb.getDetailPicture(), goodsCreateParam.getDetailPicture(), "category_id");
        DbChecker.checker(productsDb.getAfterSaleTime(), goodsCreateParam.getAfterSaleTime(), "category_id");
        DbChecker.checker(productsDb.getAfterSaleType(), goodsCreateParam.getAfterSaleType(), "category_id");
        DbChecker.checker(productsDb.getOutdated(), 0, "category_id");
        DbChecker.checker(productsDb.getStorageLocation(), 1, "category_id");
        DbChecker.checker(productsDb.getPdNo(), pdNo, "category_id");
        DbChecker.checker(productsDb.getOtherSlogan(), goodsCreateParam.getOtherSlogan(), "category_id");
        DbChecker.checker(productsDb.getPicturePath(), goodsCreateParam.getPicturePath(), "category_id");
        DbChecker.checker(productsDb.getRefundType(), goodsCreateParam.getRefundType(), "category_id");
        DbChecker.checker(productsDb.getQualityTime(), goodsCreateParam.getQualityTime(), "category_id");
        DbChecker.checker(productsDb.getQualityTimeUnit(), goodsCreateParam.getQualityTimeUnit(), "category_id");
        DbChecker.checker(productsDb.getWarnTime(), goodsCreateParam.getWarnTime(), "category_id");
        DbChecker.checker(productsDb.getCreateType(), 0, "category_id");
        DbChecker.checker(productsDb.getRealName(), goodsCreateParam.getRealName(), "category_id");
        DbChecker.checker(productsDb.getCreateRemark(), goodsCreateParam.getCreateRemark(), "category_id");
        DbChecker.checker(productsDb.getAuditStatus(), 1, "category_id");
        DbChecker.checker(productsDb.getQualityTimeType(), goodsCreateParam.getQualityTimeType(), "category_id");
    }
}
