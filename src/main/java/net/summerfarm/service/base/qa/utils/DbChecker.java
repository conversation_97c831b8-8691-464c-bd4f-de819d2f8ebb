package net.summerfarm.service.base.qa.utils;


import org.testng.Assert;

public class DbChecker {

    /**
     * 校验字段
     * @param target 实际值
     * @param source 期望值
     * @param message 当前校验的字段
     */
    public static void checker(Object target, Object source, String message){
        try {
            if (target.getClass() != source.getClass()){
                System.out.println(message + "校验不通过:" +  "expected ["+ target.getClass().getSimpleName() +"] but found [" + source.getClass().getSimpleName() +"]");
                return;
            }
            Assert.assertEquals(target, source, message);
        }catch (Error e){
            System.out.println(message + "校验不通过: " + e.getMessage());
        }
    }
}
