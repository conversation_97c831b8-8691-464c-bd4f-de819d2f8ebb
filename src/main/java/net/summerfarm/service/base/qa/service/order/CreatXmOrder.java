package net.summerfarm.service.base.qa.service.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.org.h2.util.New;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.domain.xianmudb.Inventory;
import net.summerfarm.service.base.qa.mapper.xianmudb.AreaMapper;
import net.summerfarm.service.base.qa.mapper.SupplierCoordinationConfigMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.*;
import net.summerfarm.service.base.qa.model.*;
import net.summerfarm.service.base.qa.service.MultiThreadService;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.service.goods.CreateNewGoods;
import net.summerfarm.service.base.qa.service.goods.node.GoodsNode;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alibaba.fastjson.JSON.parseObject;
import static org.reflections.Reflections.log;

@Service
public class CreatXmOrder {
    @Resource
    private TokenService tokenService;
    @Resource
    AreaMapper areaMapper;

    @Resource
    CreateNewGoods createNewGoods;

    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private  GoodsNode goodsNode;

    @Resource
    private ConfigValue configValue;
    @Resource
    private WarehouseLogisticsMappingMapper warehouseLogisticsMappingMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    public String creatOrder(String phone, String skuId, Integer num, String env, Integer skuType, Integer orderType) throws Exception {

        System.out.println("这里开始");
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String returnBody ="";
        LambdaQueryWrapper<Contact> contactWrapper = new LambdaQueryWrapper<>();
        //Integer warehouseno=473;
        if (StringUtils.isEmpty(phone) ) {
            phone = "13175000000";
        }else {
            contactWrapper.eq(Contact::getPhone, phone);
            List<Contact> contactDbs = contactMapper.selectList(contactWrapper);
            if (contactDbs.isEmpty()){
                log.info("{}:该客户不存在，无法下单",phone);
                return "该客户不存在，无法下单";
            }
        }
        //查询用户contact相关信息

        contactWrapper.eq(Contact::getPhone, phone).eq(Contact::getStatus, 1);
        List<Contact> contactDbs = contactMapper.selectList(contactWrapper);
        Long contactId = contactDbs.get(0).getContactId();
        Integer storeNo= contactDbs.get(0).getStoreNo();

        //查用户对应的库存仓
        LambdaQueryWrapper<WarehouseLogisticsMapping> WarehouseLogisticsMappingWrapper = new LambdaQueryWrapper<>();
        WarehouseLogisticsMappingWrapper.eq(WarehouseLogisticsMapping::getStoreNo,storeNo);
        List<WarehouseLogisticsMapping> warehouseLogisticsMappingDbs = warehouseLogisticsMappingMapper.selectList(WarehouseLogisticsMappingWrapper);


        //查询用户mid相关信息
        LambdaQueryWrapper<Merchant> MerchantWrapper = new LambdaQueryWrapper<>();
        MerchantWrapper.eq(Merchant::getPhone, phone);
        List<Merchant> merchantDbs = merchantMapper.selectList(MerchantWrapper);
        Long mId = merchantDbs.get(0).getMId();
        long areaNo =  merchantDbs.get(0).getAreaNo();

        Merchant entityMerchant = new Merchant();
        entityMerchant.setRoleId(-1);
        merchantMapper.update(entityMerchant, MerchantWrapper);


        //获取parent_no
        LambdaQueryWrapper<Area> AreaWrapper = new LambdaQueryWrapper<>();
        AreaWrapper.eq(Area::getAreaNo, areaNo);
        List<Area> areaDbs = areaMapper.selectList(AreaWrapper);
        Integer parentNo =areaDbs.get(0).getParentNo();


        if (StringUtils.isEmpty(skuId) && orderType == 5 )
        {
            log.info("POP类型的订单请输入SKU");
            return "POP类型的订单请输入SKU";
        }
        if (StringUtils.isEmpty(env) || (!env.equals("qa") && !env.equals("dev"))) {
            env = "dev";
        }
        if ((skuType == null )|| (skuType != 1 && skuType != 3)) {
            skuType = 3;
        }

            if (StringUtils.isEmpty(skuId)) {
            //skuId = "";
            skuId = createNewGoods.NewGoods("自动下单的商品",skuType.toString(),String.valueOf(areaNo));

            //更新可售库存

        }else {
            LambdaQueryWrapper<Inventory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Inventory::getSku, skuId);
            List<Inventory> inventoriesDb = inventoryMapper.selectList(lambdaQueryWrapper);
            if(inventoriesDb.isEmpty()){
                log.info("{}这个sku 不存在",skuId);
                return "sku 不存在";
            }
        }
        //根据运营区域寻找对应的库存仓
        String logisticsResult = okHttpUtils.sendGet(configValue.summerfarmManageUri + "/warehouse/logistics/usable-storage?areaNo="+areaNo+"&sku="+skuId+"", null, headers);
        JSONObject resultLogistics = new JSONObject(logisticsResult);
        JSONArray dataLogistics = new JSONArray(resultLogistics.get("data").toString());
        if (dataLogistics.length()==0){
            log.info(areaNo+"{}","找不到对应的根据运营区域寻找对应的库存仓");
            return "500";
        }
        JSONObject Logistics = new JSONObject(dataLogistics.get(0).toString());
        String warehouseno = Logistics.getString("warehouseNo");

        LambdaQueryWrapper<WarehouseInventoryMapping> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WarehouseInventoryMapping::getSku, skuId).eq(WarehouseInventoryMapping::getWarehouseNo,warehouseno);
        List<WarehouseInventoryMapping> MappingDb = warehouseInventoryMappingMapper.selectList(lambdaQueryWrapper);
        if (MappingDb.isEmpty()){
            String data1 = " {\n" +
                    "        \"warehouseNo\": \""+warehouseno+"\",\n" +
                    "        \"parentNo\": \""+parentNo+"\",\n" +
                    "        \"areaNo\": \""+areaNo+"\",\n" +
                    "        \"openSale\": 3,\n" +
                    "        \"price\": 1,\n" +
                    "        \"mType\": 0,\n" +
                    "        \"cornerStatus\": 1,\n" +
                    "        \"salesMode\": 0,\n" +
                    "        \"ladderPrice\": \"[]\",\n" +
                    "        \"sku\": \""+skuId+"\",\n" +
                    "        \"seconds\": 0,\n" +
                    "        \"info\": \"\",\n" +
                    "        \"_index\": 0,\n" +
                    "        \"_rowKey\": \""+areaNo+"\",\n" +
                    "        \"show\": true\n" +
                    "    }";
            JSONObject areaSku = new JSONObject(data1);
            areaSku.put("warehouseNo", warehouseno);//473是自动下单的仓库
            areaSku.put("sku", skuId);
            areaSku.put("_rowKey", storeNo);
            areaSku.put("areaNo", areaNo);
            areaSku.put("parentNo", parentNo);
            ArrayList areaSkus = new ArrayList<>();
            areaSkus.add(areaSku);
            Map<String, String> params = new HashMap<>();
            params.put("param", areaSkus.toString());
            /**
             商品上架
             */
            String result = okHttpUtils.sendCustomJsonPut(configValue.summerfarmManageUri + "/area-sku/batch", areaSkus.toString(), headers);

        }
        lambdaQueryWrapper.eq(WarehouseInventoryMapping::getSku, skuId).eq(WarehouseInventoryMapping::getWarehouseNo,warehouseno);
        List<WarehouseInventoryMapping> MappingDbs = warehouseInventoryMappingMapper.selectList(lambdaQueryWrapper);
        for (int i=0;i<MappingDbs.size();i++){
            Integer contactStoreNo = MappingDbs.get(i).getStoreNo();
            contactWrapper.eq(Contact::getPhone,phone).eq(Contact::getStatus,1).eq(Contact::getStoreNo,contactStoreNo);
            List<Contact> contactDb = contactMapper.selectList(contactWrapper);
            if (!contactDb.isEmpty()){
                storeNo= contactDb.get(0).getStoreNo();
                contactId = contactDb.get(0).getContactId();
                break;
            }
        }



        LambdaQueryWrapper<AreaStore> updateAreaStore = new LambdaQueryWrapper<>();
        updateAreaStore.eq(AreaStore::getAreaNo,warehouseno).eq(AreaStore::getSku,skuId);
        List<AreaStore> storesNumDb = areaStoreMapper.selectList(updateAreaStore);
        if (storesNumDb.get(0).getOnlineQuantity()<num){
            AreaStore entityAreaStore = new AreaStore();
            entityAreaStore.setOnlineQuantity(999);
            entityAreaStore.setChange(0);
            areaStoreMapper.update(entityAreaStore, updateAreaStore);
        }

        if ((num==null)|| num <= 0) {
            num = 5;
        }
        Map<String,Object> orderInfo =new HashMap<>() ;
        orderInfo.put("phone",phone);
        orderInfo.put("num",num);
        orderInfo.put("skuId",skuId);
        orderInfo.put("token",token);
        orderInfo.put("contactId",contactId);
        orderInfo.put("mId",mId);
        orderInfo.put("areaNo",areaNo);

        if (orderType == 1){
            returnBody=createXmOrder(orderInfo);
        } else if (orderType == 2){
            returnBody= createSample(orderInfo);
        } else if (orderType == 3){
            returnBody=createAftersale(orderInfo);
        } else if (orderType == 4){
            returnBody = createTimingOrder(orderInfo);
        } else {
            return "订单类型错误";
        }
        System.out.println(returnBody);
        log.info("订单基本信息：{}",returnBody);
        return returnBody;
    }

    /**
     * 样品申请单
     * @return
     */
    public  String createSample(Map<String,Object> orderInfo)throws Exception{
        JSONObject returnBody = new JSONObject();
        //查看SKU信息
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", orderInfo.get("token").toString());
        String result = okHttpUtils.sendGet(configValue.summerfarmManageUri + "/inventory/skus/samplePool/"+orderInfo.get("areaNo")+"?queryStr="+orderInfo.get("skuId"),"null", headers);
        System.out.println(result);
        JSONObject resultPreorder = new JSONObject(result);
        JSONArray jsonArray = new JSONArray(resultPreorder.get("data").toString());
        JSONObject dataPreorder = new JSONObject(jsonArray.getString(0).toString());
        dataPreorder.put("amount",orderInfo.get("num"));
        JSONArray sampleSkuList =new JSONArray();
        sampleSkuList.put(dataPreorder);
        JSONObject requestBody = new JSONObject();
        long remark = System.currentTimeMillis();
        requestBody.put("mId",orderInfo.get("mId"));
        requestBody.put("mphone",orderInfo.get("phone"));
        requestBody.put("areaNo",orderInfo.get("areaNo"));
        requestBody.put("mcontact","测试敏");
        requestBody.put("contactId",orderInfo.get("contactId"));
        requestBody.put("sampleSkuList",sampleSkuList);
        requestBody.put("remark",remark);
        String result1 = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/crm-service/sampleApply/insert",requestBody.toString(), headers);
        System.out.println(result1);
        JSONObject resultSample =  new JSONObject(result1);
        if (resultSample.get("success").equals(false)){
            returnBody.put("code",500);
            returnBody.put("msg","服务异常，样品申请返回出错" );
            log.info("sku 样品申请有误{}",returnBody);
            return returnBody.toString();
        }
        //样品ID
        LambdaQueryWrapper<SampleApply> sampleWrapper = new LambdaQueryWrapper<>();
        sampleWrapper.eq(SampleApply::getRemark, remark);
        List<SampleApply> sampleDbs = sampleApplyMapper.selectList(sampleWrapper);
        Integer sampleId = sampleDbs.get(0).getSampleId();
        //审核
        JSONObject reviewbody = new JSONObject(result1);
        reviewbody.put("sampleId",sampleId);
        String reviewResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/crm-service/sampleApplyReview/review",reviewbody.toString(), headers);
        System.out.println(reviewResult);
        JSONObject reviewResults = new JSONObject(reviewResult);
        if (reviewResults.get("success").equals(false)){
            returnBody.put("code",500);
            returnBody.put("msg","系统异常，样品审核失败" );
            log.info("sku 样品审核有误{}",returnBody);
            return returnBody.toString();
        }
        returnBody.put("code：",200);
        returnBody.put("手机号：",orderInfo.get("phone"));
        returnBody.put("商品sku：",orderInfo.get("skuId"));
        returnBody.put("样品订单编号：",sampleId);
        log.info("sku 样品申请成功{}",returnBody);
        return returnBody.toString();
    }

    /**
     * 商城下单
     * @param orderInfo
     * @return
     * @throws Exception
     */
    public String createXmOrder(Map<String,Object> orderInfo)throws Exception{
        //
        JSONObject returnBody = new JSONObject();


        String xmtoken = tokenService.getToken(1, "<EMAIL>", "hello1234");
        String url = "https://devh5.summerfarm.net//openid";
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", orderInfo.get("token").toString());
        String tokens =  getOpenToken(orderInfo.get("phone").toString());
        headers.put("Token", tokens);

        String shoppingbody = "{\n" +
                "    \"orderItemList\": [\n" +
                "        {\n" +
                "            \"quantity\": "+orderInfo.get("num")+",\n" +
                "            \"id\": 0,\n" +
                "            \"sku\": \""+orderInfo.get("skuId")+"\",\n" +
                "            \"productType\": 0\n" +
                "        }\n" +
                "    ],\n" +
                "    \"contactId\": \""+orderInfo.get("contactId")+"\",\n" +
                "    \"outTimes\": 0,\n" +
                "    \"usedCoupons\": [\n" +
                "        \n" +
                "    ],\n" +
                "    \"payment\": 0,\n" +
                "    \"isTakePrice\": 1,\n" +
                "    \"deliveryRulesType\": 1\n" +
                "}";
        log.info("商城下单信息：{}",shoppingbody);
        //订单提交
        String result1 = okHttpUtils.sendCustomJsonPost("https://devh5.summerfarm.net" + "/order/upsert/place-order/v3",shoppingbody, headers);
        System.out.println(result1);
        JSONObject resultPreorder = new JSONObject(result1);
        if(!resultPreorder.get("msg").equals("请求成功")){
            returnBody.put("code",500);
            returnBody.put("msg","下单失败，下单客户无法下单该sku");
            return returnBody.toString();
        }
        JSONObject dataPreorder = new JSONObject(resultPreorder.get("data").toString());
        String masterOrderNo = dataPreorder.getString("masterOrderNo");
        String subOrderNos = dataPreorder.getString("subOrderNos");
        // 解析JSON数组
        JSONArray jsonArray = new JSONArray(subOrderNos);
        String subOrderNo = jsonArray.getString(0);

        //支付
        //先充值
        LambdaQueryWrapper<Merchant> MerchantWrapper = new LambdaQueryWrapper<>();
        MerchantWrapper.eq(Merchant::getMId, orderInfo.get("mId"));
        Merchant entityMerchant = new Merchant();
        BigDecimal amount = new BigDecimal("1000000");
        entityMerchant.setRechargeAmount(amount);
        merchantMapper.update(entityMerchant, MerchantWrapper);
        Map<String, Object> payParams = new HashMap<>();
        payParams.put("orderNo",subOrderNo);
        payParams.put("type",1);
        payParams.put("payurl","//devh5.summerfarm.net/home.html");
        String resultPay = okHttpUtils.sendCustomJsonPost("https://devh5.summerfarm.net" + "/payment/pay?masterOrderNo="+masterOrderNo+"&payChannel=1",payParams.toString(), headers);

        returnBody.put("code",200);
        returnBody.put("手机号：",orderInfo.get("phone"));
        returnBody.put("商品sku：",orderInfo.get("skuId"));
        returnBody.put("订单编号：",subOrderNo);
        log.info("商城下单成功：{}",returnBody);
        return returnBody.toString();
    }

    public String createPopOrder(){
        return "";
    }
    public  String createAftersale(Map<String,Object> orderInfo) throws Exception {
        JSONObject xmOrder = new JSONObject(createXmOrder(orderInfo));
        JSONObject returnBody = new JSONObject();
        if (xmOrder.get("code").equals(500))
        {
            returnBody.put("code",500);
            returnBody.put("msg","订单创建失败，无法申请售后单");
            return returnBody.toString();
        }
        String afterbody ="{\n" +
                "\t\"orderNo\": \"${order_no}\",\n" +
                "\t\"sku\": \"${sku}\",\n" +
                "\t\"suitId\": 0,\n" +
                "\t\"afterSaleUnit\": \"件\",\n" +
                "\t\"handleType\": 7,\n" +
                "\t\"quantity\": 1,\n" +
                "\t\"handleNum\": 0,\n" +
                "\t\"deliveryed\": 1,\n" +
                "\t\"afterSaleRemarkType\": 9,\n" +
                "\t\"recoveryType\": 0,\n" +
                "\t\"recoveryNum\": 0,\n" +
                "\t\"afterSaleType\": \"商品品质问题\",\n" +
                "\t\"carryingGoods\":0\n" +
                "}";
        JSONObject afterbodys = new JSONObject(afterbody);
        afterbodys.put("orderNo",xmOrder.getString("订单编号："));
        afterbodys.put("sku",orderInfo.get("skuId"));
        afterbodys.put("quantity",orderInfo.get("num"));
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", orderInfo.get("token").toString());
        log.info("售后提交信息：{}",afterbodys);
        String resultAfter = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/after-sale/order/save",afterbodys.toString(), headers);
        JSONObject resultAfters =new JSONObject(resultAfter);
        if (resultAfters.get("success").equals(false)){
            returnBody.put("code",500);
            returnBody.put("msg","售后单申请失败");
            log.info("售后结果：{}",returnBody);
            return returnBody.toString();
        }
        //查询用户售后信息
        LambdaQueryWrapper<AfterSaleOrder> afterOrderWrapper = new LambdaQueryWrapper<>();
        afterOrderWrapper.eq(AfterSaleOrder::getOrderNo, xmOrder.getString("订单编号："));
        List<AfterSaleOrder> afterOrderDbs = afterSaleOrderMapper.selectList(afterOrderWrapper);
        String afterOrder = afterOrderDbs.get(0).getAfterSaleOrderNo();
        returnBody.put("code",200);
        returnBody.put("商品sku：",orderInfo.get("skuId"));
        returnBody.put("手机号",orderInfo.get("phone"));
        returnBody.put("售后单号",afterOrder);
        log.info("售后结果：{}",returnBody);
        return returnBody.toString();
    }
    String AddSku(String skuType,String area){
        String goodsInfo="";
        goodsNode.goodsCreate(goodsInfo);

        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        String body = "{\"configId\":\"\",\"province\":\"河南\",\"city\":\"洛阳市\",\"areaList\":[{\"adCode\":\"410324\",\"city\":\"洛阳市\",\"area\":\"栾川县\"}],\"timeList\":[{\"beginTime\":\"01:00\",\"endTime\":\"02:00\"}]}";
        String url = "https://dev2admin.summerfarm.net/summerfarm-wnc/precise-delivery-config/upsert/save";
        Map<String, String> params = new HashMap<>();
        params.put("url", url);
        params.put("param", body);
        params.put("Token", token);
        return "";
    }
    public String createTimingOrder(Map<String,Object> orderInfo) throws Exception{
        //先查询在区域内是否有省心送活动
        JSONObject returnBody = new JSONObject();
        Map<String, String> headers = new HashMap<>();
        String sku = orderInfo.get("skuId").toString();
        Integer areaNo = Integer.parseInt(orderInfo.get("areaNo").toString());
        headers.put("Token", orderInfo.get("token").toString());
        String timingPage ="{\n" +
                "\t\"timingSku\": \""+sku+"\",\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 30,\n" +
                "\t\"areaNoList\": ["+orderInfo.get("areaNo")+"]\n" +
                "}";
        String pageResponse = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri+ "/timing-delivery/query/page",timingPage, headers);
        JSONObject pageJson = new JSONObject(pageResponse);
        JSONObject pageData =new JSONObject(pageJson.get("data").toString());
        JSONArray pageList = new JSONArray(pageData.get("list").toString());
        if (pageList.length()==0){
            //没有的话需要新建省心送活动
            String timingBody = "{\n" +
                    "    \"timingSku\": \""+sku+"\",\n" +
                    "    \"deliveryUnit\": 1,\n" +
                    "    \"deliveryUpperLimit\": 999,\n" +
                    "    \"name\": \"测试\",\n" +
                    "    \"ruleInformation\": \"test\",\n" +
                    "    \"display\": 1,\n" +
                    "    \"areaNoList\": [\n" +
                    "        "+areaNo+"\n" +
                    "    ],\n" +
                    "    \"autoCalculate\": true,\n" +
                    "    \"type\": 0,\n" +
                    "    \"deliveryStart\": \"\",\n" +
                    "    \"deliveryStartType\": 0,\n" +
                    "    \"threshold\": 1,\n" +
                    "    \"deliveryPeriod\": 91\n" +
                    "}";
            String timingResponse = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri+ "/timing-delivery",timingBody, headers);

            if (!JSON.parseObject(timingResponse).get("status").toString().equals("200")){
                returnBody.put("code",500);
                returnBody.put("msg","省心送活动创建失败");
                return returnBody.toString();
            }
        }

        //下单省心送订单
        String tokens =  getOpenToken(orderInfo.get("phone").toString());
        headers.put("Token", tokens);
        LambdaQueryWrapper<Merchant> MerchantWrapper = new LambdaQueryWrapper<>();
        MerchantWrapper.eq(Merchant::getMId, orderInfo.get("mId"));
        Merchant entityMerchant = new Merchant();
        BigDecimal amount = new BigDecimal("1000000");
        entityMerchant.setRechargeAmount(amount);
        merchantMapper.update(entityMerchant, MerchantWrapper);
        //查询省心送的id
        LambdaQueryWrapper<TimingRule> timingWrapper = new LambdaQueryWrapper<>();
        timingWrapper.eq(TimingRule::getTimingSku, sku)
                .eq(TimingRule::getAreaNo,areaNo);
        List<TimingRule> timingRulesDb = timingRuleMapper.selectList(timingWrapper);
        Integer timingId = timingRulesDb.get(0).getId();
        //省心送下单
        String orderBody ="{\n" +
                "  \"mphone\": \""+orderInfo.get("phone")+"\",\n" +
                "  \"mcontact\": \""+orderInfo.get("contactId")+"\",\n" +
                "  \"quantity\": 20,\n" +
                "  \"timingRuleId\": \""+timingId+"\"\n" +
                "}";
     String createTimingOrder = okHttpUtils.sendCustomJsonPost("https://devh5.summerfarm.net" + "/order/timing",orderBody, headers);
        if (!JSON.parseObject(createTimingOrder).get("code").toString().equals("SUCCESS")){
            returnBody.put("code",500);
            returnBody.put("msg","省心送订单创建失败");
            return returnBody.toString();
        }
        String orderNo = JSON.parseObject(createTimingOrder).get("data").toString();
        //订单支付
//        Map<String, Object> payParams = new HashMap<>();
//        payParams.put("orderNo",orderNo);
//        payParams.put("type",1);
//        payParams.put("payurl","//devh5.summerfarm.net/home.html");
        String payParams ="orderNo="+orderNo+"&payurl=//devh5.summerfarm.net/home.html&type=1";
        String resultPay = okHttpUtils.sendFormPost("https://devh5.summerfarm.net" + "/wechatpay/JsapiPay",payParams.toString(), headers);
        returnBody.put("code",200);
        returnBody.put("手机号：",orderInfo.get("phone"));
        returnBody.put("商品sku：",orderInfo.get("skuId"));
        returnBody.put("省心送订单编号：",orderNo);
        log.info("省心送下单成功：{}",returnBody);
        return returnBody.toString();
    }
    public String getOpenToken(String phone)throws Exception{
        DigestUtils DigestUtils = null;
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = currentDate.format(formatter);
        String input= phone+formattedDate+"login";
        //加密
        String sign = DigestUtils.md5DigestAsHex(input.getBytes());

        String url = "https://devh5.summerfarm.net//openid";
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        String result = okHttpUtils.sendGet("http://devh5.summerfarm.net" + "/openid?phone="+phone+"&sign="+sign,"null", headers);
        JSONObject results = new JSONObject(result);
        JSONObject datas = new JSONObject(results.get("data").toString());
        String tokens =  datas.getString("token");

        return tokens;
    }
}
