package net.summerfarm.service.base.qa.mapper.cosfodb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.cosfodb.ProductSkuPreferentialCostPriceMapping;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 省心定报价城市关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@DS("COFSO_DB")
@Mapper
public interface ProductSkuPreferentialCostPriceMappingMapper extends BaseMapper<ProductSkuPreferentialCostPriceMapping> {

}
