package net.summerfarm.service.base.qa.service.impl;

import com.alibaba.fastjson.JSON;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class TokenServiceImpl implements TokenService {

    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    @Resource
    ConfigValue configValue;

    /**
     *
     * @param type
     * @param userName
     * @param password
     * @return
     */
    @Override
    public String getToken(Integer type, String userName, String password) {
        if (type == 1){
            String url = configValue.summerfarmManageUri + "/authentication/auth/username/login";
            String body = "username=" + URLEncoder.encode(userName) +"&password=" + URLEncoder.encode(password);
            Map<String, String> headers = new HashMap<String,String>();
            String result = okHttpUtils.sendFormPost(url, body,headers);
            return JSON.parseObject(result).getJSONObject("data").getString("token");
        }
        return null;
    }

    @Override
    public String getHeader(Integer type){
        String token = getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> headers = new HashMap<String,String>();
        headers.put("Token", token);
        return headers.toString();
    }

    @Override
    public String getXmAdminToken(String env, String userName, String password) {
        String body = "username=" + URLEncoder.encode(userName) +"&password=" + URLEncoder.encode(password);
        Map<String, String> headers = new HashMap<String,String>();
        String url = getBaseDomain(env) +"/authentication/auth/username/login";
        String result = okHttpUtils.sendFormPost(url, body,headers);
        return JSON.parseObject(result).getJSONObject("data").getString("token");
    }

    @Override
    public String getBaseDomain(String env) {
        if (Objects.equals(env, "qa")){
            return "https://qa" + configValue.baseSummerfarmManageUri;
        } else if (Objects.equals(env, "dev")) {
            return "https://dev" + configValue.baseSummerfarmManageUri;
        }else
        {
            return "https://dev2" + configValue.baseSummerfarmManageUri;
        }
    }
}
