package net.summerfarm.service.base.qa.service.supply;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.mapper.xianmudb.*;
import net.summerfarm.service.base.qa.model.*;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class SupplyService {
    @Resource
    private TokenService tokenService;
    @Resource
    private PurchasesMapper purchasesMapper;
    @Resource
    private WmsMissionSourcePropertyMapper wmsMissionSourcePropertyMapper;
    @Resource
    private WmsContainerMapper wmsContainerMapper;
    @Resource
    private WmsWarehouseConfigMapper wmsWarehouseConfigMapper;
    @Resource
    private WmsCabinetMapper wmsCabinetMapper;
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;
    @Resource
    private ConfigValue configValue;
    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    public String  purchaseCreate(String skus,String warehouseNo,Integer quantity) throws Exception {
        System.out.println("采购单开始");

        if ((quantity==null)|| quantity <= 0) {
            quantity = 100;
        }
        JSONArray purchasesPlanResultVOS = new JSONArray();
        Date currentDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormat.format(currentDate);
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        String[] sku = skus.split(",");
        for (int i = 0; i < sku.length; i++)
        {
            if (StringUtils.isEmpty(sku[0]) || StringUtils.isEmpty(warehouseNo)) {
                return "sku 信息和仓库不能为空";
            }

        LambdaQueryWrapper<WarehouseStorageCenter> warehouseWrapper = new LambdaQueryWrapper<>();
        warehouseWrapper.eq(WarehouseStorageCenter::getWarehouseNo, warehouseNo);
        List<WarehouseStorageCenter> WarehouseDbs = warehouseStorageCenterMapper.selectList(warehouseWrapper);
        if (WarehouseDbs.isEmpty()) {
            return "仓库不存在";
        }
        String warehouseName = WarehouseDbs.get(0).getWarehouseName();
        JSONObject skubody = new JSONObject();
        skubody.put("queryStr", sku[i]);
        skubody.put("warehouseNo", warehouseNo);
        headers.put("Token", token);
        String resultAfter = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/inventory/selectLikeBySkuOrName", skubody.toString(), headers);
        System.out.println("-----" + resultAfter);
        JSONObject resultAfters = new JSONObject(resultAfter);
        JSONObject dataStr = new JSONObject(resultAfters.get("data").toString());
        JSONArray skuInfos = new JSONArray(dataStr.get("list").toString());
        if (skuInfos.length() == 0) {
            return sku[i] + "这个SKU输入有误,未查询到此商品";
        }
        JSONObject skuInfo = new JSONObject(skuInfos.get(0).toString());
        String pdNo = skuInfo.getString("pdNo");
        //供货目录
        String spubody = "{\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 10,\n" +
                "\t\"spu\": \"" + pdNo + "\",\n" +
                "\t\"supplierIds\": [4610],\n" +
                "\t\"warehouseNos\": [" + warehouseNo + "]\n" +
                "}";
        String spuResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/pms-service/supply-list/query/page", spubody.toString(), headers);
        JSONObject spuResults = new JSONObject(spuResult);
        JSONObject spuStr = new JSONObject(spuResults.get("data").toString());
        JSONArray spuInfos = new JSONArray(spuStr.get("list").toString());
            String supplierName = "";
            String supplierId = "4610";

        String pdName = skuInfo.getString("productName");
        if (spuInfos.length() == 0) {
            JSONObject addSupplyBody = new JSONObject();
            addSupplyBody.put("spu", pdNo);
            addSupplyBody.put("pdName", pdName);
            addSupplyBody.put("warehouseNo", warehouseNo);
            addSupplyBody.put("warehouseName", warehouseName);
            addSupplyBody.put("supplierId", 4610);
            addSupplyBody.put("supplierName", "上海爱仕达自动化系统有限公司");
            supplierName = "上海爱仕达自动化系统有限公司";
            addSupplyBody.put("channelType", 1);
            addSupplyBody.put("orderModel", 1);
            addSupplyBody.put("advanceDay", 1);
            addSupplyBody.put("defaultSupplier", 0);
            String supplyResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/pms-service/supply-list/upsert/add", addSupplyBody.toString(), headers);
            //加供货目录
            System.out.println(supplyResult);
        }
        if (spuInfos.length() > 0) {
            JSONObject spuInfo = new JSONObject(spuInfos.getString(0));
            supplierId = spuInfo.getString("supplierId");
            supplierName = spuInfo.getString("supplierName");
            //获取供货信息 供应商
        }
        String weight = skuInfo.getString("weight");
        String purchasesPlanResult = "{\n" +
                "            \"latestArrivalDate\": \"" + formattedDate + "\",\n" +
                "            \"price\": 120,\n" +
                "            \"checkReport\": \"\",\n" +
                "            \"qualityTime\": 2,\n" +
                "            \"qualityTimeUnit\": \"\",\n" +
                "            \"purchaseNo\": \"\",\n" +
                "            \"purchasesPlans\": [\n" +
                "                {\n" +
                "                    \"checkReport\": \"\",\n" +
                "                    \"qualityDate\": \"\",\n" +
                "                    \"productionDate\": \"\",\n" +
                "                    \"inQuantity\": 0,\n" +
                "                    \"supplierId\": \"\",\n" +
                "                    \"priceType\": 0,\n" +
                "                    \"latestArrivalDate\": \"" + formattedDate + "\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"quantity\": "+quantity+",\n" +
                "            \"singlePlice\": 1.2,\n" +
                "            \"sku\": \"" + sku[i] + "\",\n" +
                "            \"title\": \"" + pdName + "\",\n" +
                "            \"weight\": \"" + weight + "\",\n" +
                "            \"pdName\": \"" + pdName + "\",\n" +
                "            \"extType\": 0,\n" +
                "            \"isSave\": false,\n" +
                "            \"isNew\": true,\n" +
                "            \"type\": 0,\n" +
                "            \"advQuantity\": 0,\n" +
                "            \"minPrice\": 100,\n" +
                "            \"maxPrice\": 100,\n" +
                "            \"priceType\": 0,\n" +
                "            \"priceLapse\": false,\n" +
                "            \"pdNo\": \"" + pdNo + "\",\n" +
                "            \"netWeight\": 2,\n" +
                "            \"supplier\": \"" + supplierName + "\",\n" +
                "            \"supplierName\": \"" + supplierName + "\",\n" +
                "            \"supplierId\": " + supplierId + ",\n" +
                "            \"showTip\": false,\n" +
                "            \"priceHint\": false,\n" +
                "            \"roadSaleRatio\": 0\n" +
                "        }";
        System.out.println(purchasesPlanResult);
        JSONObject purchasesPlanResultVO = new JSONObject(purchasesPlanResult);
        purchasesPlanResultVOS.put(purchasesPlanResultVO);

    }

        String remark = String.valueOf(System.currentTimeMillis());
        String addSupply="{\n" +
                "    \"purchaseNo\": \"\",\n" +
                "    \"purchasePlace\": \"\",\n" +
                "    \"purchaser\": \"测试哈哈\",\n" +
                "    \"receiver\": \"测试哈哈\",\n" +
                "    \"remark\": \""+remark+"\",\n" +
                "    \"purchaseTime\": \""+formattedDate+"\",\n" +
                "    \"state\": 1,\n" +
                "    \"purchasesPlanResultVOS\":         "+purchasesPlanResultVOS.toString()+",\n" +
                "    \"purchasesType\": 0,\n" +
                "    \"arrangeTime\": \""+formattedDate+"\",\n" +
                "    \"arrangeRemark\": \"\",\n" +
                "    \"creatorId\": 123,\n" +
                "    \"deliveryTime\": \"\",\n" +
                "    \"tmsDistSiteName\": \"\",\n" +
                "    \"deliveryAddress\": \"\",\n" +
                "    \"tmsDistSiteId\": \"\",\n" +
                "    \"areaNo\": "+warehouseNo+",\n" +
                "    \"operatorId\": 123\n" +
                "}";

        //添加采购单
        String poResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/pms-service/po/upsert/add",addSupply.toString(), headers);
        System.out.println(poResult);
        //查询得到的采购单
        LambdaQueryWrapper<Purchases> purchasesWrapper = new LambdaQueryWrapper<>();
        purchasesWrapper.eq(Purchases::getRemark, remark);
        List<Purchases> purchasesDbs = purchasesMapper.selectList(purchasesWrapper);
        String purchaseNo = purchasesDbs.get(0).getPurchaseNo();
        Integer state = purchasesDbs.get(0).getState();
        if (state==3)
        {
            return purchaseNo+"需要飞书审核";
        }
        if (state ==2){
            return purchaseNo+"该供应商需要待供应商确认";

        }
        try {
            // 使用 TimeUnit 让线程睡眠2秒
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String aaa =inStore(purchaseNo,warehouseNo,quantity);
        //待gong
        return purchaseNo;
    }
    public String inStore(String purchaseNo,String warehouseNo,int quantity)throws Exception{
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        Map<String, String> headers = new HashMap<>();
        headers.put("token",token);
        String listBody = "{\n" +
                "\t\"dates\": [],\n" +
                "\t\"sourceId\": \""+purchaseNo+"\",\n" +
                "\t\"pageIndex\": 1,\n" +
                "\t\"pageSize\": 50,\n" +
                "\t\"queryType\": 0\n" +
                "}";
        String taskResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/in-store/query/list", listBody,headers );

        System.out.println(taskResult);
        JSONObject jsonObject = new JSONObject(taskResult);
        JSONObject dataObject = jsonObject.getJSONObject("data");
        JSONArray listArray = dataObject.getJSONArray("list");
        Calendar calendar = Calendar.getInstance();
        long currentTimeTimestamp = calendar.getTimeInMillis();
        calendar.add(Calendar.DAY_OF_MONTH, 300);
        JSONArray  orderDetails = new JSONArray();
        // 获取时间戳
        JSONObject taskObject = listArray.getJSONObject(0);
        int taskId  = taskObject.getInt("taskId");
        long timestamp = calendar.getTimeInMillis();
        //获取任务详情信息

        String taskRequest = "{\"taskId\": "+taskId+",\"queryType\": 0}";
        String taskDetailResult = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/in-store/query/detail", taskRequest,headers );

        System.out.println(taskDetailResult);
        JSONObject inTaskObject = new JSONObject(taskDetailResult);
        JSONObject inTaskDate = inTaskObject.getJSONObject("data");
        JSONArray taskArray = inTaskDate.getJSONArray("resDTOList");
        int listLenth = taskArray.length();
        LambdaQueryWrapper<WmsWarehouseConfig> wConfigWrapper = new LambdaQueryWrapper<>();
        wConfigWrapper.eq(WmsWarehouseConfig::getWarehouseNo, warehouseNo).eq(WmsWarehouseConfig::getConfigKey,"cabinetStatus");
        List<WmsWarehouseConfig> wConfigDbs = wmsWarehouseConfigMapper.selectList(wConfigWrapper);
        String container="";
        if (!wConfigDbs.isEmpty()){
            if(wConfigDbs.get(0).getConfigValue().equals("1"))
            {
                System.out.println("库位精细化");
                //去获取收货容器
                List<Integer> Purpose = Arrays.asList(1,5);
                LambdaQueryWrapper<WmsContainer> containerWrapper = new LambdaQueryWrapper<>();
                containerWrapper
                        .eq(WmsContainer::getWarehouseNo, warehouseNo)
                        .eq(WmsContainer::getOccupyStatus,0)
                        .in(WmsContainer::getPurpose,Purpose)
                        .eq(WmsContainer::getContainerStatus,1)
                        .ne(WmsContainer::getContainerCode,"MR00001");
                List<WmsContainer> WmsContainerDbs = wmsContainerMapper.selectList(containerWrapper);
                container=WmsContainerDbs.get(0).getContainerCode();
                //精细化确认收货
                JSONObject arrival =new JSONObject();
                arrival.put("stockStorageTaskId",taskId);
                arrival.put("warehouseNo",warehouseNo);
                arrival.put("operator",123);

                String arrivalResponse =okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/in-store/upsert/arrival_cargo", arrival.toString(),headers );
                System.out.println(arrivalResponse);
                //判断是否是新品，是新品要维护新品信息
            }
        }

        String sku="";
        for (int i = 0; i < listLenth; i++){
            JSONObject taskDetails = taskArray.getJSONObject(i);
            String pdName = taskDetails.getString("pdName");
            sku =taskDetails.getString("sku");
            JSONObject orderDetail = new JSONObject();
            if (!wConfigDbs.isEmpty()){
                if (wConfigDbs.get(0).getConfigValue().equals("1"))
                {
                    String isNew = taskDetails.getString("isNew");
                    if (isNew.equals("0")) {
                        //维护新品
                        String newSave = "{\n" +
                                "    \"cargoInfoList\": [\n" +
                                "        {\n" +
                                "            \"warehouseNo\": " + warehouseNo + ",\n" +
                                "            \"sku\": \"" + sku + "\",\n" +
                                "            \"weightNum\": 10,\n" +
                                "            \"stackRule\": \"1*1\",\n" +
                                "            \"capacity\": \"0.10*0.10*0.10\"\n" +
                                "        }\n" +
                                "    ]\n" +
                                "}";

                        String newResponse = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/cargo/upsert/new_save", newSave.toString(), headers);
                        System.out.println(newResponse);
                    }
                    //容器赋值
                    orderDetail.put("receivingContainer",container);
                }
            }


            orderDetail.put("sku",sku);
            orderDetail.put("pdName",pdName);
            orderDetail.put("purchaseNo",purchaseNo);
            orderDetail.put("stockNum",quantity);
            orderDetail.put("produceAt",currentTimeTimestamp);
            orderDetail.put("shelfLife",timestamp);
            orderDetails.put(orderDetail);
        }

        JSONObject taskObjects = new JSONObject();
        taskObjects.put("warehouseNo",warehouseNo);
        taskObjects.put("stockStorageTaskId",taskId);
        taskObjects.put("type",0);
        taskObjects.put("bizId",purchaseNo);
        taskObjects.put("orderDetails",orderDetails);

        String inbound = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/inbound/create", taskObjects.toString(),headers );
        System.out.println(inbound);
//查询仓库是否是自营仓,自营仓需要上架到库位
        if (!wConfigDbs.isEmpty()){
            if (wConfigDbs.get(0).getConfigValue().equals("1"))
            {
                //获取上架任务
                LambdaQueryWrapper<WmsMissionSourceProperty> WmsMissionWrapper = new LambdaQueryWrapper<>();
                WmsMissionWrapper.eq(WmsMissionSourceProperty::getSourceOrderNo,purchaseNo);
                List<WmsMissionSourceProperty> WmsMissionDbs = wmsMissionSourcePropertyMapper.selectList(WmsMissionWrapper);
                if (WmsMissionDbs.isEmpty()){
                    return "";
                }
                String missionNo = WmsMissionDbs.get(0).getMissionNo();

                //分配操作员
                String assignBody = "{\n" +
                        "\t\"operators\": [{\n" +
                        "\t\t\"operatorId\": 123,\n" +
                        "\t\t\"operatorName\": \"测试哈哈\"\n" +
                        "\t}],\n" +
                        "\t\"missionNo\": \""+missionNo+"\",\n" +
                        "\t\"warehouseNo\": "+warehouseNo+"\n" +
                        "}";
                String assignResponse = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/mission/upsert/assign", assignBody.toString(),headers );

                LambdaQueryWrapper<WmsCabinet> wmsCabinetWrapper = new LambdaQueryWrapper<>();
                List<String> cabinetCodes = Arrays.asList( "JJ01", "CY01","SH01","YK01","DX01","XN01","JH01","JG01","KN01");
                wmsCabinetWrapper.eq(WmsCabinet::getWarehouseNo,warehouseNo)
                        .notIn(WmsCabinet::getCabinetCode,cabinetCodes)
                        .ne(WmsCabinet::getZoneCode,"MR01")
                        .orderByDesc(WmsCabinet::getId);
                List<WmsCabinet> WmsCabinetDbs = wmsCabinetMapper.selectList(wmsCabinetWrapper);
                String cabinetCode = WmsCabinetDbs.get(0).getCabinetCode();
                // 创建SimpleDateFormat对象
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                // 将毫秒数转换为Date对象
                Date resultDate = new Date(currentTimeTimestamp);
                // 使用SimpleDateFormat格式化Date对象
                String produceTime = sdf.format(resultDate);
                String shelfLife = sdf.format(timestamp);

                //上架到库位
                String commitBody = "{\n" +
                        "\t\"missionNo\": \""+missionNo+"\",\n" +
                        "\t\"warehouseNo\": \""+warehouseNo+"\",\n" +
                        "\t\"commitInfo\": [{\n" +
                        "\t\t\"sourceContainerNo\": \""+container+"\",\n" +
                        "\t\t\"produceTime\": \""+produceTime+"\",\n" +
                        "\t\t\"targetCabinetNo\": \""+cabinetCode+"\",\n" +
                        "\t\t\"dealNum\": "+quantity+",\n" +
                        "\t\t\"sku\": \""+sku+"\",\n" +
                        "\t\t\"shelfLife\": \""+shelfLife+"\"\n" +
                        "\t}]\n" +
                        "}";

                String commitResponse = okHttpUtils.sendCustomJsonPost(configValue.summerfarmManageUri + "/summerfarm-wms/shelving/upsert/commit", commitBody,headers );
                System.out.println(commitResponse);
            }
        }

        return "";
    }
}
