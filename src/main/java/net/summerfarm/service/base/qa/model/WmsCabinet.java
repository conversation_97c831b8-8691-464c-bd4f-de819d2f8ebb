package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 库位信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-299 15:43:31
 */
@Getter
@Setter
@TableName("wms_cabinet")
public class WmsCabinet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 库位编码
     */
    @TableField("cabinet_code")
    private String cabinetCode;

    /**
     * 库位类型（1：高位货架，2：地位货架，3：地堆）
     */
    @TableField("cabinet_type")
    private Integer cabinetType;

    /**
     * 库位属性（1：拣货区，2：存储区，3：暂存区）
     */
    @TableField("purpose")
    private Integer purpose;

    /**
     * 长（单位：m）
     */
    @TableField("length")
    private Double length;

    /**
     * 宽（单位：m）
     */
    @TableField("width")
    private Double width;

    /**
     * 高（单位：m）
     */
    @TableField("high")
    private Double high;

    /**
     * 所属库区id
     */
    @TableField("zone_id")
    private Long zoneId;

    /**
     * 所属库区编码
     */
    @TableField("zone_code")
    private String zoneCode;

    /**
     * 所属库区名称
     */
    @TableField("zone_name")
    private String zoneName;

    /**
     * 是否允许混放sku（0：不允许，1：允许）
     */
    @TableField("allow_mixed_sku")
    private Integer allowMixedSku;

    /**
     * 允许sku混放数量
     */
    @TableField("allow_mixed_sku_quantity")
    private Integer allowMixedSkuQuantity;

    /**
     * 是否允许混放效期（0：不允许，1：允许）
     */
    @TableField("allow_mixed_period")
    private Integer allowMixedPeriod;

    /**
     * 允许效期混放数量
     */
    @TableField("allow_mixed_period_quantity")
    private Integer allowMixedPeriodQuantity;

    /**
     * 是否允许混放批次（0：不允许，1：允许）
     */
    @TableField("allow_mixed_batch")
    private Integer allowMixedBatch;

    /**
     * 允许批次混放数量
     */
    @TableField("allow_mixed_batch_quantity")
    private Integer allowMixedBatchQuantity;

    /**
     * 库位顺序
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 库位状态（0：禁用，1：启用）
     */
    @TableField("cabinet_status")
    private Integer cabinetStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_operator")
    private String createOperator;

    /**
     * 更新人
     */
    @TableField("update_operator")
    private String updateOperator;

    /**
     * 承重量（单位：kg）
     */
    @TableField("load_weight")
    private Double loadWeight;

    /**
     * 初始化库位标签（0：非系统初始化库位，1：系统初始化库位）
     */
    @TableField("init_option")
    private Integer initOption;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 当前体积
     */
    @TableField("current_volume")
    private Double currentVolume;

    /**
     * 逻辑体积，0.7当前体积
     */
    @TableField("logic_volume")
    private Double logicVolume;

    /**
     * 当前数量
     */
    @TableField("current_quantity")
    private Long currentQuantity;


}
