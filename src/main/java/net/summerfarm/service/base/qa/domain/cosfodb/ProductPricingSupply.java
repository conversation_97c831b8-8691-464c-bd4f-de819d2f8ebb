package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供价实体类
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("product_pricing_supply")
public class ProductPricingSupply implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应SKU id
     */
    private Long supplySkuId;

    /**
     * 供应商tenantId
     */
    private Long supplyTenantId;

    /**
     * 是否关联商品
     */
    private Integer associated;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
