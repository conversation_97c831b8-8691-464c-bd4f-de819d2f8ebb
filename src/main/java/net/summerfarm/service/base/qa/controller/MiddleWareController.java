package net.summerfarm.service.base.qa.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.DTO.ATPRedisRequestDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 类<code>Doc</code>用于：TODO
 * 中间件调用
 * <AUTHOR>
 * @ClassName MiddleWareController
 * @date 2025-03-13
 */
@Slf4j
@RestController
@RequestMapping(value = "/middleWare")
public class MiddleWareController {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisTemplate<String, Object> redisTemplateDB5;

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.database}")
    private int redisDatabase;


    @PostMapping(value = "/referenceInvoke")
    public String redisInvoke(@RequestBody String param) {
        log.info("redisInvoke param:{}", param);
        ATPRedisRequestDto atpRedisRequestDto = JSONObject.parseObject(param, ATPRedisRequestDto.class);
        Object res = new Object();
        RedisConnectionFactory factory = redisTemplate.getConnectionFactory();
        LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) factory;
        LettuceConnectionFactory lettuceFactoryDB5 = (LettuceConnectionFactory) redisTemplateDB5.getConnectionFactory();
        // 确保是Lettuce客户端（根据实际客户端类型调整）
        if (lettuceFactory instanceof LettuceConnectionFactory) {
            String host = lettuceFactory.getHostName();
            int port = lettuceFactory.getPort();
            int dbIndex = lettuceFactory.getDatabase();
            int dbIndexDB5 = lettuceFactoryDB5.getDatabase();
            log.info("res-Host: " + host + ", Port: " + port + ", DB Index: " + dbIndex);
            log.info("res5-db5:{}", dbIndexDB5);
        } else {
            log.error("Unsupported Redis client type");
        }
        try {
            if ("5".equals(atpRedisRequestDto.getDbIndex())){
                log.info("切换至DB5");
                if ("set" .equals(atpRedisRequestDto.getOperation())){
                    redisTemplateDB5.opsForValue().set(atpRedisRequestDto.getQueryKey(),
                            atpRedisRequestDto.getQueryContent(),
                            atpRedisRequestDto.getTimeout(),
                            atpRedisRequestDto.getTimeUnit());
                    return "success";
                }else {
                    DataType type = redisTemplateDB5.type((String) atpRedisRequestDto.getQueryContent());
                    log.info("redisInvoke type:{}", type);
                    if (type == DataType.STRING) {
                        res = redisTemplateDB5.opsForValue().get((String) atpRedisRequestDto.getQueryContent());
                    } else if (type == DataType.HASH) {
                        res = redisTemplateDB5.opsForHash().entries((String) atpRedisRequestDto.getQueryContent()); // 获取所有字段
                    } else {
                        throw new IllegalArgumentException("Unsupported type: " + type);
                    }
                }
            }else {
                log.info("切换至DB0");
                if ("set" .equals(atpRedisRequestDto.getOperation())){
                    redisTemplate.opsForValue().set(atpRedisRequestDto.getQueryKey(),
                            atpRedisRequestDto.getQueryContent(),
                            atpRedisRequestDto.getTimeout(),
                            atpRedisRequestDto.getTimeUnit());
                    return "success";
                }else {
                    DataType type = redisTemplate.type((String) atpRedisRequestDto.getQueryContent());
                    log.info("redisInvoke type:{}", type);
                    if (type == DataType.STRING) {
                        res = redisTemplate.opsForValue().get((String) atpRedisRequestDto.getQueryContent());
                    } else if (type == DataType.HASH) {
                        res = redisTemplate.opsForHash().entries((String) atpRedisRequestDto.getQueryContent()); // 获取所有字段
                    } else {
                        throw new IllegalArgumentException("Unsupported type: " + type);
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            log.error(e.toString());
        }
        return JSONObject.toJSONString(res);
    }
}
