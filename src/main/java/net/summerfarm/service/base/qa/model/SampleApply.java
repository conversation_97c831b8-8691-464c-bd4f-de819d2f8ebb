package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 样品申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-144 18:09:01
 */
@Getter
@Setter
@TableName("sample_apply")
public class SampleApply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sample_id", type = IdType.AUTO)
    private Integer sampleId;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    @TableField("create_id")
    private Integer createId;

    /**
     * 创建人名称
     */
    @TableField("create_name")
    private String createName;

    /**
     * 式样用户id
     */
    @TableField("m_id")
    private Integer mId;

    /**
     * 式样用户名称
     */
    @TableField("m_name")
    private String mName;

    /**
     * 会员等级
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 用户类型 单店，大客户
     */
    @TableField("m_size")
    private String mSize;

    /**
     * 手机号
     */
    @TableField("m_phone")
    private String mPhone;

    /**
     * 联系人
     */
    @TableField("m_contact")
    private String mContact;

    /**
     * 式样用户收货地址id
     */
    @TableField("contact_id")
    private Integer contactId;

    /**
     * 客户所属用户bdid
     */
    @TableField("bd_id")
    private Integer bdId;

    /**
     * 客户归属bd名称
     */
    @TableField("bd_name")
    private String bdName;

    /**
     * 状态 0 待反馈 1 已反馈
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 客户满意度 0 未评价 1,2,3,4
     */
    @TableField("satisfaction")
    private Integer satisfaction;

    /**
     * 客户购买意向
     */
    @TableField("purchase_intention")
    private Integer purchaseIntention;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 所属城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * 样品配送时间
     */
    @TableField("delivery_time")
    private LocalDate deliveryTime;

    /**
     * 配送仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 风险识别 0正常 1风控
     */
    @TableField("risk_level")
    private Integer riskLevel;


}
