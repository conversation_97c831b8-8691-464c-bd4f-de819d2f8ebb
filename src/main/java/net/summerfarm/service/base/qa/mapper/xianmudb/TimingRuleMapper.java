package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.TimingRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 定期送规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15 15:08:06
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface TimingRuleMapper extends BaseMapper<TimingRule> {

}
