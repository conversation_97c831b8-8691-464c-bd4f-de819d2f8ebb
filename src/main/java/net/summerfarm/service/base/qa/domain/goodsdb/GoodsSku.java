package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 货品SKU表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_sku")
public class GoodsSku {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * sku编码
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * spuId
     */
    @TableField(value = "spu_id")
    private Long spuId;

    /**
     * sku名称
     */
    @TableField(value = "sku_title")
    private String skuTitle;

    /**
     * sku描述
     */
    @TableField(value = "sku_sub_title")
    private String skuSubTitle;

    /**
     * sku主图
     */
    @TableField(value = "sku_main_picture")
    private String skuMainPicture;

    /**
     * sku详情图
     */
    @TableField(value = "sku_detail_picture")
    private String skuDetailPicture;

    /**
     * 自由编码
     */
    @TableField(value = "custom_sku_code")
    private String customSkuCode;

    /**
     * SKU状态：-1、上新处理中 0、使用中 1、已删除
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 所属大客户ID
     */
    @TableField(value = "admin_id")
    private Integer adminId;

    /**
     * 代仓类型：0=自营，1=自营且代仓 2=代仓复制品
     */
    @TableField(value = "agent_type")
    private Integer agentType;

    /**
     * 税率
     */
    @TableField(value = "tax_rate_value")
    private BigDecimal taxRateValue;

    /**
     * saas侧skuId
     */
    @TableField(value = "saas_sku_id")
    private Long saasSkuId;

    /**
     * 鲜沐侧skuId
     */
    @TableField(value = "xm_sku_id")
    private Long xmSkuId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}