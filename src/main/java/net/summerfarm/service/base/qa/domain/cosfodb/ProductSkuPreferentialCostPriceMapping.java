package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 省心定报价城市关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@TableName("product_sku_preferential_cost_price_mapping")
public class ProductSkuPreferentialCostPriceMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * 省心定 id
     */
    private Long skuPreferentialCostPriceId;

    /**
     * 城市Id
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记 0未删除，1删除
     */
    private Integer deleted;


}
