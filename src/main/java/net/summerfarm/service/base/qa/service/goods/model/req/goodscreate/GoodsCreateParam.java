/**
  * Copyright 2024 bejson.com 
  */
package net.summerfarm.service.base.qa.service.goods.model.req.goodscreate;
import lombok.Data;

import java.util.List;

@Data
public class GoodsCreateParam {

    private String pdName;
    private String pddetail;
    private String brandId;
    private String otherSlogan;
    private Integer afterSaleTime;
    private Integer warnTime;
    private Integer qualityTime;
    private String qualityTimeUnit;
    private Integer qualityTimeType;
    private String refundType;
    private String realName;
    private String afterSaleType;
    private Integer storageLocation;
    private String afterSaleUnit;
    private Integer auditStatus;
    private Integer createStatus;
    private String createType;
    private String productIntroduction;
    private String picturePath;
    private String detailPicture;
    private Integer categoryId;
    private String createRemark;
    private List<String> commentPic;
    private List<KeyValueList> keyValueList;
    private List<SalePropertyList> salePropertyList;
    private List<Sku> skuList;

}