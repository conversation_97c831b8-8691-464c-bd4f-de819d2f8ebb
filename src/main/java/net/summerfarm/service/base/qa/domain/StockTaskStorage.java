package net.summerfarm.service.base.qa.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 入库任务主表
 * @TableName stock_task_storage
 */
@Data
public class StockTaskStorage implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 操作人id
     */
    private Integer adminId;

    /**
     * 来源单号
     */
    private String sourceId;

    /**
     * 入库任务类型
     */
    private Integer type;

    /**
     * 状态入库进度, 0-正常，1-已完成, 2-已取消, 3-已关闭
     */
    private Integer state;

    /**
     * 预约入库时间
     */
    private LocalDateTime expectTime;

    /**
     * 入库库存仓编号
     */
    private Integer inWarehouseNo;

    /**
     * 城配仓(货品来源仓)
     */
    private Integer outWarehouseNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库仓名称
     */
    private String inWarehouseName;

    /**
     * 出库仓名称
     */
    private String outWarehouseName;

    /**
     * 入库进度
     */
    private Integer processState;

    /**
     * 鲜果,非鲜果
     */
    private Integer category;

    /**
     * 关闭原因
     */
    private String closeReason;

    /**
     * 应入不符合原因
     */
    private String mismatchReason;

    /**
     * 旧任务id
     */
    private Long stockTaskId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 任务归属人
     */
    private String ownership;

    /**
     * 租户id(saas品牌方)
     */
    private Long tenantId;

    /**
     * 售后单号
     */
    private String afterSaleOrderNo;

    /**
     * 文件上传地址
     */
    private String fileAddress;

    /**
     * 关闭时间
     */
    private LocalDateTime closeTime;

    /**
     * 履约单号
     */
    private String fulfillmentNo;

    /**
     * 货主
     */
    private String cargoOwner;

    /**
     * 到货时间
     */
    private LocalDateTime arrivalTime;

    /**
     * 收货状态（默认未确认收货）
     */
    private Integer receivingState;

    /**
     * 任务票据
     */
    private String receiptUrl;

    /**
     * 系统来源 0-内部 1-外部
     */
    private Integer systemSource;

    /**
     * OFC货品供应单批次号
     */
    private String psoNo;

    /**
     * 幂等键，不同type类型不同规则
     */
    private String uniqueKey;

    /**
     * 三方订单号
     */
    private String thirdOrderNo;

    /**
     * 采购模式 1: 代销不入仓越库采购 2: 备货采购单 3: 预提采购单 4:POP采购 5:POP T+2采购
     */
    private Integer purchaseMode;

    /**
     * 扩展标志位
     */
    private Long optionFlag;

    /**
     * 来源订单
     */
    private String sourceOrderNo;

    /**
     * 外部仓
     */
    private String externalWarehouseNo;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StockTaskStorage other = (StockTaskStorage) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getAdminId() == null ? other.getAdminId() == null : this.getAdminId().equals(other.getAdminId()))
            && (this.getSourceId() == null ? other.getSourceId() == null : this.getSourceId().equals(other.getSourceId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
            && (this.getExpectTime() == null ? other.getExpectTime() == null : this.getExpectTime().equals(other.getExpectTime()))
            && (this.getInWarehouseNo() == null ? other.getInWarehouseNo() == null : this.getInWarehouseNo().equals(other.getInWarehouseNo()))
            && (this.getOutWarehouseNo() == null ? other.getOutWarehouseNo() == null : this.getOutWarehouseNo().equals(other.getOutWarehouseNo()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getInWarehouseName() == null ? other.getInWarehouseName() == null : this.getInWarehouseName().equals(other.getInWarehouseName()))
            && (this.getOutWarehouseName() == null ? other.getOutWarehouseName() == null : this.getOutWarehouseName().equals(other.getOutWarehouseName()))
            && (this.getProcessState() == null ? other.getProcessState() == null : this.getProcessState().equals(other.getProcessState()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getCloseReason() == null ? other.getCloseReason() == null : this.getCloseReason().equals(other.getCloseReason()))
            && (this.getMismatchReason() == null ? other.getMismatchReason() == null : this.getMismatchReason().equals(other.getMismatchReason()))
            && (this.getStockTaskId() == null ? other.getStockTaskId() == null : this.getStockTaskId().equals(other.getStockTaskId()))
            && (this.getOperatorName() == null ? other.getOperatorName() == null : this.getOperatorName().equals(other.getOperatorName()))
            && (this.getOwnership() == null ? other.getOwnership() == null : this.getOwnership().equals(other.getOwnership()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getAfterSaleOrderNo() == null ? other.getAfterSaleOrderNo() == null : this.getAfterSaleOrderNo().equals(other.getAfterSaleOrderNo()))
            && (this.getFileAddress() == null ? other.getFileAddress() == null : this.getFileAddress().equals(other.getFileAddress()))
            && (this.getCloseTime() == null ? other.getCloseTime() == null : this.getCloseTime().equals(other.getCloseTime()))
            && (this.getFulfillmentNo() == null ? other.getFulfillmentNo() == null : this.getFulfillmentNo().equals(other.getFulfillmentNo()))
            && (this.getCargoOwner() == null ? other.getCargoOwner() == null : this.getCargoOwner().equals(other.getCargoOwner()))
            && (this.getArrivalTime() == null ? other.getArrivalTime() == null : this.getArrivalTime().equals(other.getArrivalTime()))
            && (this.getReceivingState() == null ? other.getReceivingState() == null : this.getReceivingState().equals(other.getReceivingState()))
            && (this.getReceiptUrl() == null ? other.getReceiptUrl() == null : this.getReceiptUrl().equals(other.getReceiptUrl()))
            && (this.getSystemSource() == null ? other.getSystemSource() == null : this.getSystemSource().equals(other.getSystemSource()))
            && (this.getPsoNo() == null ? other.getPsoNo() == null : this.getPsoNo().equals(other.getPsoNo()))
            && (this.getUniqueKey() == null ? other.getUniqueKey() == null : this.getUniqueKey().equals(other.getUniqueKey()))
            && (this.getThirdOrderNo() == null ? other.getThirdOrderNo() == null : this.getThirdOrderNo().equals(other.getThirdOrderNo()))
            && (this.getPurchaseMode() == null ? other.getPurchaseMode() == null : this.getPurchaseMode().equals(other.getPurchaseMode()))
            && (this.getOptionFlag() == null ? other.getOptionFlag() == null : this.getOptionFlag().equals(other.getOptionFlag()))
            && (this.getSourceOrderNo() == null ? other.getSourceOrderNo() == null : this.getSourceOrderNo().equals(other.getSourceOrderNo()))
            && (this.getExternalWarehouseNo() == null ? other.getExternalWarehouseNo() == null : this.getExternalWarehouseNo().equals(other.getExternalWarehouseNo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getAdminId() == null) ? 0 : getAdminId().hashCode());
        result = prime * result + ((getSourceId() == null) ? 0 : getSourceId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getExpectTime() == null) ? 0 : getExpectTime().hashCode());
        result = prime * result + ((getInWarehouseNo() == null) ? 0 : getInWarehouseNo().hashCode());
        result = prime * result + ((getOutWarehouseNo() == null) ? 0 : getOutWarehouseNo().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getInWarehouseName() == null) ? 0 : getInWarehouseName().hashCode());
        result = prime * result + ((getOutWarehouseName() == null) ? 0 : getOutWarehouseName().hashCode());
        result = prime * result + ((getProcessState() == null) ? 0 : getProcessState().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getCloseReason() == null) ? 0 : getCloseReason().hashCode());
        result = prime * result + ((getMismatchReason() == null) ? 0 : getMismatchReason().hashCode());
        result = prime * result + ((getStockTaskId() == null) ? 0 : getStockTaskId().hashCode());
        result = prime * result + ((getOperatorName() == null) ? 0 : getOperatorName().hashCode());
        result = prime * result + ((getOwnership() == null) ? 0 : getOwnership().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getAfterSaleOrderNo() == null) ? 0 : getAfterSaleOrderNo().hashCode());
        result = prime * result + ((getFileAddress() == null) ? 0 : getFileAddress().hashCode());
        result = prime * result + ((getCloseTime() == null) ? 0 : getCloseTime().hashCode());
        result = prime * result + ((getFulfillmentNo() == null) ? 0 : getFulfillmentNo().hashCode());
        result = prime * result + ((getCargoOwner() == null) ? 0 : getCargoOwner().hashCode());
        result = prime * result + ((getArrivalTime() == null) ? 0 : getArrivalTime().hashCode());
        result = prime * result + ((getReceivingState() == null) ? 0 : getReceivingState().hashCode());
        result = prime * result + ((getReceiptUrl() == null) ? 0 : getReceiptUrl().hashCode());
        result = prime * result + ((getSystemSource() == null) ? 0 : getSystemSource().hashCode());
        result = prime * result + ((getPsoNo() == null) ? 0 : getPsoNo().hashCode());
        result = prime * result + ((getUniqueKey() == null) ? 0 : getUniqueKey().hashCode());
        result = prime * result + ((getThirdOrderNo() == null) ? 0 : getThirdOrderNo().hashCode());
        result = prime * result + ((getPurchaseMode() == null) ? 0 : getPurchaseMode().hashCode());
        result = prime * result + ((getOptionFlag() == null) ? 0 : getOptionFlag().hashCode());
        result = prime * result + ((getSourceOrderNo() == null) ? 0 : getSourceOrderNo().hashCode());
        result = prime * result + ((getExternalWarehouseNo() == null) ? 0 : getExternalWarehouseNo().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", adminId=").append(adminId);
        sb.append(", sourceId=").append(sourceId);
        sb.append(", type=").append(type);
        sb.append(", state=").append(state);
        sb.append(", expectTime=").append(expectTime);
        sb.append(", inWarehouseNo=").append(inWarehouseNo);
        sb.append(", outWarehouseNo=").append(outWarehouseNo);
        sb.append(", remark=").append(remark);
        sb.append(", inWarehouseName=").append(inWarehouseName);
        sb.append(", outWarehouseName=").append(outWarehouseName);
        sb.append(", processState=").append(processState);
        sb.append(", category=").append(category);
        sb.append(", closeReason=").append(closeReason);
        sb.append(", mismatchReason=").append(mismatchReason);
        sb.append(", stockTaskId=").append(stockTaskId);
        sb.append(", operatorName=").append(operatorName);
        sb.append(", ownership=").append(ownership);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", afterSaleOrderNo=").append(afterSaleOrderNo);
        sb.append(", fileAddress=").append(fileAddress);
        sb.append(", closeTime=").append(closeTime);
        sb.append(", fulfillmentNo=").append(fulfillmentNo);
        sb.append(", cargoOwner=").append(cargoOwner);
        sb.append(", arrivalTime=").append(arrivalTime);
        sb.append(", receivingState=").append(receivingState);
        sb.append(", receiptUrl=").append(receiptUrl);
        sb.append(", systemSource=").append(systemSource);
        sb.append(", psoNo=").append(psoNo);
        sb.append(", uniqueKey=").append(uniqueKey);
        sb.append(", thirdOrderNo=").append(thirdOrderNo);
        sb.append(", purchaseMode=").append(purchaseMode);
        sb.append(", optionFlag=").append(optionFlag);
        sb.append(", sourceOrderNo=").append(sourceOrderNo);
        sb.append(", externalWarehouseNo=").append(externalWarehouseNo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}