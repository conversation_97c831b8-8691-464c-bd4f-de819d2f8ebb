package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.supply.SupplyService;
import net.summerfarm.service.base.qa.service.supply.TmsService;
import net.summerfarm.service.base.qa.service.supply.WmsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/supply")
public class SupplyController {
    @Resource
    private SupplyService supplyService;
    @Resource
    private TmsService tmsService;
    @Resource
    private WmsService wmsService;
    @GetMapping(value = "/purchaseCreate")
    public String createPurchase(@RequestParam(value = "skus", required = false ) String skus,
                                 @RequestParam(value = "quantity", required = false ) Integer quantity,
                                 @RequestParam(value = "warehouseNo", required = false ) String warehouseNo)throws Exception{
        return supplyService.purchaseCreate(skus,warehouseNo,quantity);
    }

    @GetMapping(value = "/tms")
    public  String tmsCutoff(@RequestParam(value = "order", required = false) String order,
                             @RequestParam(value = "complete", required = false) String complete)throws Exception{
        return tmsService.tmsCutoff(order,complete);
    }

    @GetMapping(value = "/wmsDeliver")
    public  String wmsDeliver(@RequestParam(value = "order", required = false) String order,
                              @RequestParam(value = "isWave", required = false) String isWave)throws Exception{
        return wmsService.wmsDeliver(order,isWave);
    }

}
