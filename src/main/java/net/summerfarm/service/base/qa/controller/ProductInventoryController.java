package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.datachecker.ProductInventoryService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 商货品中心数据对比controller
 */
@RestController
@RequestMapping(value = "/product/checker")
public class ProductInventoryController {

    @Resource
    ProductInventoryService productInventoryService;

    @GetMapping(value = "/product/inventory")
    public Map<String, String> ProductInventoryChecker(){
        Map<String, String> resultMap = productInventoryService.ProductInventoryChecker();
        return resultMap;
    }

}
