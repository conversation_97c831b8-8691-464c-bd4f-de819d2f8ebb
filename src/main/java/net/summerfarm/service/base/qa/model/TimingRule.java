package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 定期送规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15 15:08:06
 */
@Getter
@Setter
@TableName("timing_rule")
public class TimingRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 团购商品sku
     */
    @TableField("timing_sku")
    private String timingSku;

    @TableField("area_no")
    private Integer areaNo;

    /**
     * 展示标记：0不展示，1展示
     */
    @TableField("display")
    private Boolean display;

    /**
     * 团购开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 团购结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 配送开始时间
     */
    @TableField("delivery_start")
    private LocalDate deliveryStart;

    /**
     * 配送结束时间
     */
    @TableField("delivery_end")
    private LocalDate deliveryEnd;

    /**
     * 规则描述
     */
    @TableField("rule_information")
    private String ruleInformation;

    /**
     * 起送数量
     */
    @TableField("delivery_unit")
    private Integer deliveryUnit;

    /**
     * 单词配送上限
     */
    @TableField("delivery_upper_limit")
    private Integer deliveryUpperLimit;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 0省心送，1预售
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 排序
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否自动计算配送次数
     */
    @TableField("auto_calculate")
    private Integer autoCalculate;

    /**
     * 配送周期
     */
    @TableField("delivery_period")
    private Integer deliveryPeriod;

    /**
     * 开始配送类型：0、下一个配送日 1、指定开始日期（delivery_start）
     */
    @TableField("delivery_start_type")
    private Integer deliveryStartType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 省心送门槛
     */
    @TableField("threshold")
    private Integer threshold;

    /**
     * 下单日期+N的N值
     */
    @TableField("plus_day")
    private Integer plusDay;


}
