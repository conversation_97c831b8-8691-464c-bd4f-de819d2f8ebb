package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.datachecker.ProductPriceService;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 商货品中心数据对比controller
 */
@RestController
@RequestMapping(value = "/price/checker")
public class ProductPriceController {

    @Resource
    ProductPriceService productPriceService;

    @GetMapping(value = "/product/price")
    public void ProductPriceChecker(){
        productPriceService.ProductPriceChecker();
    }

}
