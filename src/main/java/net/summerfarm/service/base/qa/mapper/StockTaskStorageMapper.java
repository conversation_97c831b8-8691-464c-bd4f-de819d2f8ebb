package net.summerfarm.service.base.qa.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.domain.StockTaskStorage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【stock_task_storage(入库任务主表)】的数据库操作Mapper
* @createDate 2025-06-11 18:15:30
* @Entity net.summerfarm.service.base.qa.domain.StockTaskStorage
*/
@DS("XIANMU_DB")
public interface StockTaskStorageMapper extends BaseMapper<StockTaskStorage> {


}
