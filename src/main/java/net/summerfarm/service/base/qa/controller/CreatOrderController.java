package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.order.CreatXmOrder;
import net.summerfarm.service.base.qa.service.order.OrderPayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
创建订单
 */

@RestController
@RequestMapping(value = "/order")
public class CreatOrderController {
    @Resource
    CreatXmOrder creatXmOrder;
    @Resource
    OrderPayService orderPayService;
    @GetMapping(value = "/xmOrderCreate")
    public String CreatXmOrders(@RequestParam(value = "phone", required = false ,defaultValue = "13175000000") String phone ,
                               @RequestParam(value = "skuId", required = false,defaultValue = "") String skuId,
                               @RequestParam(value = "num", required = false,defaultValue = "0") Integer num,
                               @RequestParam(value = "env", required = false,defaultValue = "dev") String env,
                               @RequestParam(value = "skuType", required = false,defaultValue = "0") Integer skuType,
                                @RequestParam(value = "orderType", required = false,defaultValue = "0") Integer orderType) throws Exception {

        return creatXmOrder.creatOrder(phone,skuId,num,env,skuType,orderType);
    }
    @GetMapping(value = "/pay")
        public  String OrderPay(@RequestParam(value = "orderNo", required = false) String order)throws Exception{
        return orderPayService.orderPay(order);
    }
    @PostMapping(value = "/timingOrderCreate")
    public String CreateTimingOrders(@RequestParam(value = "phone", required = false ,defaultValue = "13175000000") String phone ,
                                @RequestParam(value = "skuId", required = false,defaultValue = "") String skuId,
                                @RequestParam(value = "num", required = false,defaultValue = "0") Integer num,
                                @RequestParam(value = "env", required = false,defaultValue = "dev") String env,
                                @RequestParam(value = "skuType", required = false,defaultValue = "0") Integer skuType,
                                @RequestParam(value = "orderType", required = false,defaultValue = "0") Integer orderType) throws Exception {

        return creatXmOrder.creatOrder(phone,skuId,num,env,skuType,4);
    }

}
