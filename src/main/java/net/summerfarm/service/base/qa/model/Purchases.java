package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 采购单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-219 18:39:26
 */
@Getter
@Setter
@TableName("purchases")
public class Purchases implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * purchase
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 采购单号
     */
    @TableField("purchase_no")
    private String purchaseNo;

    /**
     * 采购时间
     */
    @TableField("purchase_time")
    private LocalDateTime purchaseTime;

    @TableField("supplier")
    private String supplier;

    /**
     * 采购地点
     */
    @TableField("purchase_place")
    private String purchasePlace;

    /**
     * 收货时间
     */
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 收货地点
     */
    @TableField("receive_place")
    private String receivePlace;

    /**
     * 采购负责人
     */
    @TableField("purchaser")
    private String purchaser;

    /**
     * 收货负责人
     */
    @TableField("receiver")
    private String receiver;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private LocalDateTime addTime;

    @TableField("flight_number")
    private String flightNumber;

    /**
     * 采购单状态： -1、作废 0、计划制定 1、已发布2、待供应商确认 3、审核中 4、审核拒绝
     */
    @TableField("state")
    private Integer state;

    /**
     * 数据是否同步至统冠数据库，0未同步1，已同步
     */
    @TableField("ds")
    private Boolean ds;

    /**
     * 仓库编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     *  1送货到库，2自提
     */
    @TableField("delivery_type")
    private Integer deliveryType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 运输单号
     */
    @TableField("delivery_no")
    private String deliveryNo;

    /**
     * 物流支付方式 1、我方付款 2、供应商预付 3、供应商包邮
     */
    @TableField("logistics_payment_type")
    private Integer logisticsPaymentType;

    /**
     * 物流费用
     */
    @TableField("logistics_cost")
    private BigDecimal logisticsCost;

    /**
     * 物流费结算标识 0、可结算 1、不可结算（结算中或已完成）
     */
    @TableField("logistics_settle_flag")
    private Integer logisticsSettleFlag;

    /**
     * 0表示正常采购，1表示直发采购
     */
    @TableField("purchases_type")
    private Integer purchasesType;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 发票匹配进展
     */
    @TableField("matching_progress")
    private String matchingProgress;

    /**
     * 采购单状态(发票)： 0、未归档 1、已归档
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 拆单原单号
     */
    @TableField("origin_no")
    private String originNo;

    /**
     * 可预约标识：0 不可预约1 可预约
     */
    @TableField("is_arrange")
    private Integer isArrange;

    /**
     * 入库进度：0、待入库 1、部分入库 2、已入库
     */
    @TableField("process_state")
    private Integer processState;

    /**
     * 归档时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;

    /**
     * 计划制定状态下预约入库时间
     */
    @TableField("arrange_time")
    private LocalDate arrangeTime;

    /**
     * 计划制定状态下预约备注
     */
    @TableField("arrange_remark")
    private String arrangeRemark;

    /**
     * 供应商确认状态0未确认1已确认2已拒绝
     */
    @TableField("operator_type")
    private Integer operatorType;

    /**
     * 操作人id
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 采购员id
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 来源:xianmu,saas
     */
    @TableField("`source`")
    private String source;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 录入人名
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 物流用车时间
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 物流用车发货地址
     */
    @TableField("tms_dist_site_id")
    private Long tmsDistSiteId;

    /**
     * 业务类型：1-代销不入库类型 2-代销不入仓-备货 3-代销不入仓-预提
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 提货时间
     */
    @TableField("take_time")
    private LocalDateTime takeTime;

    /**
     * ofc采购供应单号
     */
    @TableField("pso_no")
    private String psoNo;


}
