package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 类目属性映射表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_property_mapping")
public class GoodsPropertyMapping {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类目id
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 属性id
     */
    @TableField(value = "products_property_id")
    private Long productsPropertyId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}