//package net.summerfarm.service.base.qa.utils;
//
//import net.summerfarm.service.base.qa.service.es.AICaseService;
//import net.summerfarm.service.base.qa.service.es.ESServiceImpl;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@Component
//public class AICaseScheduledTask {
//
//    @Resource
//    private AICaseService aICaseService;
//
//    @Resource
//    private ESServiceImpl esServiceImpl;
//
//    // 每小时执行一次（可通过cron表达式调整）
//    @Scheduled(cron = "0 0 * * * ?")
//    public void executeESQuery() {
////        List<String> projectIds = aICaseService.findProjectIdsByStatus(0);
////        if (!projectIds.isEmpty()) {
////            esServiceImpl.compositeSearchByProjectIds(projectIds);
////            // 可根据业务需要补充ES查询结果处理逻辑
////        }
//    }
//}