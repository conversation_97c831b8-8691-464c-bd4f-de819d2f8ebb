package net.summerfarm.service.base.qa.controller.pms.purchase;


import net.summerfarm.service.base.qa.controller.pms.purchase.input.PurchaseCreateInput;
import net.summerfarm.service.base.qa.service.pms.purchase.impl.PurchasesService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

@RestController
@RequestMapping(value = "/pms", name = "采购管理")
public class PurchaseController {


    @Resource
    private PurchasesService purchasesService;

    /**
     * 鲜沐--普通采购入库流程
     * @param input
     * @return
     */
    @PostMapping(value = "/xmPurchaseCreate",name = "鲜沐--普通采购入库流程")
    public CommonResult<String> purchaseCreate(@RequestBody(required = false) PurchaseCreateInput input){
        try {
            PurchaseCreateInput finalInput = Optional.ofNullable(input)
                    .map(i -> i.mergeWithDefaults(i))
                    .orElse(new PurchaseCreateInput());
            return CommonResult.ok(purchasesService.purchaseCreate(finalInput));
        }catch (Exception e){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,e.getMessage());
        }
    }
}
