package net.summerfarm.service.base.qa.mapper.cosfodb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.cosfodb.MarketItemPrice;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品价格表(MarketItemPrice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-28 15:45:04
 */
@Mapper
@DS("COFSO_DB")
public interface MarketItemPriceMapper extends BaseMapper<MarketItemPrice>{

}

