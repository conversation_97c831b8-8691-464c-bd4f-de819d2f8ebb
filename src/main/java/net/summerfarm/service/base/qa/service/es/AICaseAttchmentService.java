package net.summerfarm.service.base.qa.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class AICaseAttchmentService {

    private static final Logger logger = Logger.getLogger(AICaseAttchmentService.class.getName());

    // 飞书知识库空间ID常量
    private static final String FEISHU_SPACE_ID = "7259361245839622172";

    // 飞书API端点
    private static final String FEISHU_SEARCH_API = "https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size=3";
    private static final String FEISHU_CONTENT_API = "https://open.feishu.cn/open-apis/docx/v1/documents/%s/raw_content?lang=0";

    private final OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    /**
     * 获取Wiki内容
     * @param query 搜索查询词
     * @param token 授权Bearer token
     * @return JSONObject 包含query和content字段
     */
    public JSONObject getWikiContent(String query, String token) {
        JSONObject result = new JSONObject();
        result.put("query", query);

        try {
            logger.info("Starting wiki content search for query: " + query);

            // 第一步：搜索Wiki节点，获取所有节点ID
            JSONArray nodeIds = searchWikiNodes(query, token);
            if (nodeIds == null || nodeIds.isEmpty()) {
                result.put("content", "未找到相关文档");
                logger.warning("No wiki nodes found for query: " + query);
                return result;
            }

            logger.info("Found " + nodeIds.size() + " node(s) for query: " + query);

            // 第二步：遍历所有节点ID，尝试获取文档内容
            StringBuilder combinedContent = new StringBuilder();
            int successCount = 0;

            for (int i = 0; i < nodeIds.size(); i++) {
                JSONObject nodeInfo = nodeIds.getJSONObject(i);
                String nodeId = nodeInfo.getString("node_id");
                String title = nodeInfo.getString("title");

                logger.info("Processing node " + (i + 1) + "/" + nodeIds.size() +
                           ": " + nodeId + " (title: " + title + ")");

                String content = getDocumentContent(nodeId, token);
                if (content != null && !content.trim().isEmpty()) {
                    if (successCount > 0) {
                        combinedContent.append("\n\n=== ").append(title).append(" ===\n");
                    } else {
                        combinedContent.append("=== ").append(title).append(" ===\n");
                    }
                    combinedContent.append(content);
                    successCount++;
                    logger.info("Successfully retrieved content from node: " + nodeId);
                } else {
                    logger.warning("Failed to retrieve content from node: " + nodeId);
                }
            }

            if (successCount > 0) {
                result.put("content", combinedContent.toString());
                logger.info("Successfully retrieved wiki content from " + successCount +
                           " out of " + nodeIds.size() + " nodes for query: " + query);
            } else {
                result.put("content", "找到相关文档但无法获取内容");
                logger.warning("Found nodes but failed to retrieve content for query: " + query);
            }

        } catch (Exception e) {
            logger.severe("Error getting wiki content for query '" + query + "': " + e.getMessage());
            result.put("content", "获取文档内容时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取Wiki内容（限制文档数量）
     * @param query 搜索查询词
     * @param token 授权Bearer token
     * @param maxDocuments 最大文档数量限制
     * @return JSONObject 包含query和content字段
     */
    public JSONObject getWikiContentWithLimit(String query, String token, int maxDocuments) {
        JSONObject result = new JSONObject();
        result.put("query", query);

        try {
            logger.info("Starting wiki content search for query: " + query + " with limit: " + maxDocuments);

            // 第一步：搜索Wiki节点，获取所有节点ID
            JSONArray nodeIds = searchWikiNodes(query, token);
            if (nodeIds == null || nodeIds.isEmpty()) {
                result.put("content", "未找到相关文档");
                logger.warning("No wiki nodes found for query: " + query);
                return result;
            }

            logger.info("Found " + nodeIds.size() + " node(s) for query: " + query);

            // 限制处理的文档数量
            int documentsToProcess = Math.min(nodeIds.size(), maxDocuments);
            logger.info("Processing top " + documentsToProcess + " documents");

            // 第二步：遍历限定数量的节点ID，尝试获取文档内容
            StringBuilder combinedContent = new StringBuilder();
            int successCount = 0;

            for (int i = 0; i < documentsToProcess; i++) {
                JSONObject nodeInfo = nodeIds.getJSONObject(i);
                String nodeId = nodeInfo.getString("node_id");
                String title = nodeInfo.getString("title");

                logger.info("Processing node " + (i + 1) + "/" + documentsToProcess +
                           ": " + nodeId + " (title: " + title + ")");

                String content = getDocumentContent(nodeId, token);
                if (content != null && !content.trim().isEmpty()) {
                    if (successCount > 0) {
                        combinedContent.append("\n\n=== ").append(title).append(" ===\n");
                    } else {
                        combinedContent.append("=== ").append(title).append(" ===\n");
                    }
                    combinedContent.append(content);
                    successCount++;
                    logger.info("Successfully retrieved content from node: " + nodeId);
                } else {
                    logger.warning("Failed to retrieve content from node: " + nodeId);
                }
            }

            if (successCount > 0) {
                result.put("content", combinedContent.toString());
                logger.info("Successfully retrieved wiki content from " + successCount +
                           " out of " + documentsToProcess + " nodes for query: " + query);
            } else {
                result.put("content", "找到相关文档但无法获取内容");
                logger.warning("Found nodes but failed to retrieve content for query: " + query);
            }

        } catch (Exception e) {
            logger.severe("Error getting wiki content for query '" + query + "': " + e.getMessage());
            result.put("content", "获取文档内容时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 搜索Wiki节点
     * @param query 搜索查询词
     * @param token 授权token
     * @return 节点信息数组，包含node_id和title，如果未找到则返回null
     */
    private JSONArray searchWikiNodes(String query, String token) {
        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("query", query);
            requestBody.put("space_id", FEISHU_SPACE_ID);

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);

            logger.info("Sending search request to Feishu API with query: " + query);

            // 发送POST请求
            String response = okHttpUtils.sendCustomJsonPost(FEISHU_SEARCH_API, requestBody.toJSONString(), headers);

            if (response == null || response.trim().isEmpty()) {
                logger.warning("Empty response from Feishu search API");
                return null;
            }

            logger.info("Received search response: " + response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                logger.warning("Feishu API returned error code: " + code + ", message: " + responseJson.getString("msg"));
                return null;
            }

            // 提取节点ID
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                logger.warning("No data field in response");
                return null;
            }

            JSONArray items = data.getJSONArray("items");
            if (items == null || items.isEmpty()) {
                logger.warning("No items found in search results");
                return null;
            }

            // 创建结果数组，包含所有节点的基本信息
            JSONArray nodeInfos = new JSONArray();
            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);
                String nodeId = item.getString("node_id");
                String title = item.getString("title");

                if (nodeId != null && !nodeId.trim().isEmpty()) {
                    JSONObject nodeInfo = new JSONObject();
                    nodeInfo.put("node_id", nodeId);
                    nodeInfo.put("title", title != null ? title : "未知标题");
                    nodeInfos.add(nodeInfo);

                    logger.info("Found node: " + nodeId + " with title: " + title);
                }
            }

            if (nodeInfos.isEmpty()) {
                logger.warning("No valid node_ids found in search results");
                return null;
            }

            logger.info("Total valid nodes found: " + nodeInfos.size());
            return nodeInfos;

        } catch (Exception e) {
            logger.severe("Error searching wiki nodes: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取文档内容
     * @param nodeId 节点ID
     * @param token 授权token
     * @return 文档内容，如果获取失败则返回null
     */
    private String getDocumentContent(String nodeId, String token) {
        try {
            // 构建请求URL
            String url = String.format(FEISHU_CONTENT_API, nodeId);

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);

            logger.info("Sending content request to Feishu API for node: " + nodeId);

            // 发送GET请求
            String response = okHttpUtils.sendGet(url, null, headers);

            if (response == null || response.trim().isEmpty()) {
                logger.warning("Empty response from Feishu content API");
                return null;
            }

            logger.info("Received content response: " + response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                logger.warning("Feishu API returned error code: " + code + ", message: " + responseJson.getString("msg"));
                return null;
            }

            // 提取内容
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                logger.warning("No data field in content response");
                return null;
            }

            String content = data.getString("content");
            if (content == null) {
                logger.warning("No content field in data");
                return null;
            }

            return content;

        } catch (Exception e) {
            logger.severe("Error getting document content: " + e.getMessage());
            return null;
        }
    }
}
