package net.summerfarm.service.base.qa.service.es;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import java.util.logging.Logger;

@Service
public class AICaseAttchmentService {

    private static final Logger logger = Logger.getLogger(AICaseService.class.getName());

    public JSONObject getWikiContent(String content, String token) {

        return null;
    }
}
