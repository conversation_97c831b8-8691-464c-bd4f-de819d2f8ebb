package net.summerfarm.service.base.qa.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class AICaseAttchmentService {

    private static final Logger logger = Logger.getLogger(AICaseAttchmentService.class.getName());

    // 飞书知识库空间ID常量
    private static final String FEISHU_SPACE_ID = "7259361245839622172";

    // 飞书API端点
    private static final String FEISHU_SEARCH_API = "https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size=3";
    private static final String FEISHU_CONTENT_API = "https://open.feishu.cn/open-apis/docx/v1/documents/%s/raw_content?lang=0";

    private final OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    /**
     * 获取Wiki内容
     * @param query 搜索查询词
     * @param token 授权Bearer token
     * @return JSONObject 包含query和content字段
     */
    public JSONObject getWikiContent(String query, String token) {
        JSONObject result = new JSONObject();
        result.put("query", query);

        try {
            logger.info("Starting wiki content search for query: " + query);

            // 第一步：搜索Wiki节点
            String nodeId = searchWikiNodes(query, token);
            if (nodeId == null) {
                result.put("content", "未找到相关文档");
                logger.warning("No wiki nodes found for query: " + query);
                return result;
            }

            logger.info("Found node ID: " + nodeId);

            // 第二步：获取文档内容
            String content = getDocumentContent(nodeId, token);
            result.put("content", content != null ? content : "无法获取文档内容");

            logger.info("Successfully retrieved wiki content for query: " + query);

        } catch (Exception e) {
            logger.severe("Error getting wiki content for query '" + query + "': " + e.getMessage());
            result.put("content", "获取文档内容时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 搜索Wiki节点
     * @param query 搜索查询词
     * @param token 授权token
     * @return 节点ID，如果未找到则返回null
     */
    private String searchWikiNodes(String query, String token) {
        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("query", query);
            requestBody.put("space_id", FEISHU_SPACE_ID);

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);

            logger.info("Sending search request to Feishu API with query: " + query);

            // 发送POST请求
            String response = okHttpUtils.sendCustomJsonPost(FEISHU_SEARCH_API, requestBody.toJSONString(), headers);

            if (response == null || response.trim().isEmpty()) {
                logger.warning("Empty response from Feishu search API");
                return null;
            }

            logger.info("Received search response: " + response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                logger.warning("Feishu API returned error code: " + code + ", message: " + responseJson.getString("msg"));
                return null;
            }

            // 提取节点ID
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                logger.warning("No data field in response");
                return null;
            }

            JSONArray items = data.getJSONArray("items");
            if (items == null || items.isEmpty()) {
                logger.warning("No items found in search results");
                return null;
            }

            // 获取第一个结果的node_id
            JSONObject firstItem = items.getJSONObject(0);
            String nodeId = firstItem.getString("node_id");

            if (nodeId == null || nodeId.trim().isEmpty()) {
                logger.warning("No node_id found in first search result");
                return null;
            }

            return nodeId;

        } catch (Exception e) {
            logger.severe("Error searching wiki nodes: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取文档内容
     * @param nodeId 节点ID
     * @param token 授权token
     * @return 文档内容，如果获取失败则返回null
     */
    private String getDocumentContent(String nodeId, String token) {
        try {
            // 构建请求URL
            String url = String.format(FEISHU_CONTENT_API, nodeId);

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);

            logger.info("Sending content request to Feishu API for node: " + nodeId);

            // 发送GET请求
            String response = okHttpUtils.sendGet(url, null, headers);

            if (response == null || response.trim().isEmpty()) {
                logger.warning("Empty response from Feishu content API");
                return null;
            }

            logger.info("Received content response: " + response);

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response);

            // 检查响应状态
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 0) {
                logger.warning("Feishu API returned error code: " + code + ", message: " + responseJson.getString("msg"));
                return null;
            }

            // 提取内容
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                logger.warning("No data field in content response");
                return null;
            }

            String content = data.getString("content");
            if (content == null) {
                logger.warning("No content field in data");
                return null;
            }

            return content;

        } catch (Exception e) {
            logger.severe("Error getting document content: " + e.getMessage());
            return null;
        }
    }
}
