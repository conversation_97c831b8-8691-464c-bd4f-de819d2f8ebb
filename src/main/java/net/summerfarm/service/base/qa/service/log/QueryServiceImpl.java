package net.summerfarm.service.base.qa.service.log;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class QueryServiceImpl {


    public JSONObject scan(String content)
    {
        List<JSONObject> logJsonList = new ArrayList<>();
        // 本示例从环境变量中获取AccessKey ID和AccessKey Secret。
        String accessId = "LTAI5tP2on8fRdtm7ZUZT2iZ";
        String accessKey = "******************************";
        // 输入Project名称。
        String project = "k8s-log-custom-azure-aks";
        // 设置日志服务的服务接入点。此处以杭州为例，其它地域请根据实际情况填写。
        String host = "cn-hangzhou.log.aliyuncs.com";
        // 输入Logstore名称。
        String logStore = "qa-backend-log";

        // 创建日志服务Client。
        Client client = new Client(host, accessId, accessKey);

        // 在指定的Logstore内执行查询。
        try {
            // 使用关键字path-0/file-5查询日志。
            String query = "msg:接口请求参数 and \"requestURI = [" + "/frequent-sku-pool/update/add" + "]\"";
            int time=(int) (new Date().getTime()/1000);
            int from=time-86400*15;
//            int from = (int) (new Date().getTime() / 1000 - 300);
            int to = (int) (new Date().getTime() / 1000);

            // 本示例中，query参数用于设置查询语句；line参数用于控制返回日志条数，取值为3，最大值为100。
            GetLogsResponse logsResponse = client.GetLogs(project, logStore, from, to, "", query, 10, 0,true);
            System.out.println("-------------Query is started.-------------");
            System.out.println("Returned query result count :" + logsResponse.GetCount());
            System.out.println("from time is :" + from);
            System.out.println("to time is :" + to);
            for (QueriedLog log : logsResponse.getLogs()) {
                LogItem item = log.GetLogItem();
                System.out.println("log time : " + item.mLogTime);
                System.out.println("Jsonstring : " + item.ToJsonString());
                // 提取 msg 和 traceId 字段
                String msg = JSONObject.parseObject(item.ToJsonString()).getString("msg");
                String traceId = JSONObject.parseObject(item.ToJsonString()).getString("traceId");
                // 创建新的 JSONObject 并添加所需的字段
                JSONObject filteredJson = new JSONObject();
                if (!msg.isEmpty()) {
                    filteredJson.put("msg", msg);
                }
                if (!traceId.isEmpty()) {
                    filteredJson.put("traceId", traceId);
                }

                // 添加到最终的 JSON 列表中
                logJsonList.add(filteredJson);
//                logJsonList.add(JSONObject.parseObject(item.ToJsonString()));
            }
            System.out.println("-------------Query is finished.-------------");

        } catch (LogException e) {
            System.out.println("LogException e :" + e.toString());
            System.out.println("error code :" + e.GetErrorCode());
            System.out.println("error message :" + e.GetErrorMessage());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("msg", "success");
        jsonObject.put("data", logJsonList);
        return jsonObject;
    }
}
