package net.summerfarm.service.base.qa.service.log;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.summerfarm.service.base.qa.mapper.repeater.InterfaceLogMapper;
import net.summerfarm.service.base.qa.model.InterfaceLog;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class QueryServiceImpl {

    @Resource
    private InterfaceLogMapper interfaceLogMapper;


    public JSONObject scan(String content)
    {
        List<JSONObject> logJsonList = new ArrayList<>();
        // 本示例从环境变量中获取AccessKey ID和AccessKey Secret。
        String accessId = "LTAI5tP2on8fRdtm7ZUZT2iZ";
        String accessKey = "******************************";
        // 输入Project名称。
        String project = "k8s-log-custom-azure-aks";
        // 设置日志服务的服务接入点。此处以杭州为例，其它地域请根据实际情况填写。
        String host = "cn-hangzhou.log.aliyuncs.com";
        // 输入Logstore名称。
        String logStore = "qa-backend-log";

        // 创建日志服务Client。
        Client client = new Client(host, accessId, accessKey);

        // 在指定的Logstore内执行查询。
        try {
            // 使用关键字path-0/file-5查询日志。
            String query = "msg:接口请求参数 and \"requestURI = [" + content + "]\"";
            int time=(int) (new Date().getTime()/1000);
            int from=time-86400*15;
//            int from = (int) (new Date().getTime() / 1000 - 300);
            int to = (int) (new Date().getTime() / 1000);

            // 本示例中，query参数用于设置查询语句；line参数用于控制返回日志条数，取值为3，最大值为100。
            GetLogsResponse logsResponse = client.GetLogs(project, logStore, from, to, "", query, 10, 0,true);
            System.out.println("-------------Query is started.-------------");
            System.out.println("Returned query result count :" + logsResponse.GetCount());
            System.out.println("from time is :" + from);
            System.out.println("to time is :" + to);
            for (QueriedLog log : logsResponse.getLogs()) {
                LogItem item = log.GetLogItem();
                System.out.println("log time : " + item.mLogTime);
                System.out.println("Jsonstring : " + item.ToJsonString());
                // 提取 msg 和 traceId 字段
                String msg = JSONObject.parseObject(item.ToJsonString()).getString("msg");
                String traceId = JSONObject.parseObject(item.ToJsonString()).getString("traceId");
                // 创建新的 JSONObject 并添加所需的字段
                JSONObject filteredJson = new JSONObject();
                if (!msg.isEmpty()) {
                    filteredJson.put("msg", msg);
                }
                if (!traceId.isEmpty()) {
                    filteredJson.put("traceId", traceId);
                }

                // 添加到最终的 JSON 列表中
                logJsonList.add(filteredJson);
//                logJsonList.add(JSONObject.parseObject(item.ToJsonString()));
            }
            System.out.println("-------------Query is finished.-------------");

        } catch (LogException e) {
            System.out.println("LogException e :" + e.toString());
            System.out.println("error code :" + e.GetErrorCode());
            System.out.println("error message :" + e.GetErrorMessage());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("msg", "success");
        jsonObject.put("data", logJsonList);
        return jsonObject;
    }

    /**
     * 查询interface_log表的记录，并进行分页
     * 返回id、interfaceName、context、flag和scaned字段
     */
    public List<Map<String, Object>> query(int pageNum, int pageSize) {
        // 创建分页对象
        Page<InterfaceLog> page = new Page<>(pageNum, pageSize);

        // 创建查询条件
        LambdaQueryWrapper<InterfaceLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(InterfaceLog::getId);

        // 执行分页查询
        IPage<InterfaceLog> resultPage = interfaceLogMapper.selectPage(page, queryWrapper);

        // 转换结果，只保留指定字段
        return resultPage.getRecords().stream()
                .map(interfaceLog -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", interfaceLog.getId());
                    map.put("interfaceName", interfaceLog.getInterfaceName());
                    map.put("context", interfaceLog.getContext());
                    map.put("flag", interfaceLog.getFlag());
                    map.put("scaned", interfaceLog.getScaned());
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 统计interface_log表的记录总数
     */
    public long countInterfaceLogs() {
        return interfaceLogMapper.selectCount(null);
    }

    /**
     * 创建新的InterfaceLog记录
     * 检查interfaceName是否已存在，如果不存在则创建新记录
     */
    public boolean createInterfaceLog(String interfaceName) {
        // 检查是否已存在相同的interfaceName
        LambdaQueryWrapper<InterfaceLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InterfaceLog::getInterfaceName, interfaceName);
        Long existingCount = interfaceLogMapper.selectCount(queryWrapper);

        if (existingCount > 0) {
            return false; // 已存在，返回false
        }

        // 创建新记录
        InterfaceLog interfaceLog = new InterfaceLog();
        interfaceLog.setInterfaceName(interfaceName);
        interfaceLog.setFlag(0L); // 默认值
        interfaceLog.setScaned(0L); // 默认值

        int result = interfaceLogMapper.insert(interfaceLog);
        return result > 0;
    }

    /**
     * 更新InterfaceLog记录
     * 根据id查找记录并更新指定字段
     */
    public boolean updateInterfaceLog(Long id, String interfaceName, String context, Long flag, Long scaned) {
        // 先检查记录是否存在
        InterfaceLog existingLog = interfaceLogMapper.selectById(id);
        if (existingLog == null) {
            return false; // 记录不存在
        }

        // 更新记录
        InterfaceLog updateLog = new InterfaceLog();
        updateLog.setId(id);
        updateLog.setInterfaceName(interfaceName);
        updateLog.setContext(context);
        updateLog.setFlag(flag);
        updateLog.setScaned(scaned);

        int result = interfaceLogMapper.updateById(updateLog);
        return result > 0;
    }
}
