package net.summerfarm.service.base.qa.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 供货目录（品仓供应商信息）
 * @TableName pms_supply_list
 */
@Data
public class PmsSupplyList implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * SPUID
     */
    private String spu;

    /**
     * SPU名称
     */
    private String pdName;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 渠道类型,1源头直采；2分销市场
     */
    private Integer channelType;

    /**
     * 是否为默认供应商，0否；1是
     */
    private Integer defaultSupplier;

    /**
     * 订货模式，1不定期不定量；2不定期定量；3定期不定量；4定期定量
     */
    private Integer orderModel;

    /**
     * 定期时间，多个用逗号隔开
     */
    private String fixedTime;

    /**
     * 提前期（非负数，最多不超过30天）
     */
    private Integer advanceDay;

    /**
     * 来源,xianmu,saas
     */
    private String source;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 后台三级类目
     */
    private Long categoryId;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PmsSupplyList other = (PmsSupplyList) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpu() == null ? other.getSpu() == null : this.getSpu().equals(other.getSpu()))
            && (this.getPdName() == null ? other.getPdName() == null : this.getPdName().equals(other.getPdName()))
            && (this.getWarehouseNo() == null ? other.getWarehouseNo() == null : this.getWarehouseNo().equals(other.getWarehouseNo()))
            && (this.getWarehouseName() == null ? other.getWarehouseName() == null : this.getWarehouseName().equals(other.getWarehouseName()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getChannelType() == null ? other.getChannelType() == null : this.getChannelType().equals(other.getChannelType()))
            && (this.getDefaultSupplier() == null ? other.getDefaultSupplier() == null : this.getDefaultSupplier().equals(other.getDefaultSupplier()))
            && (this.getOrderModel() == null ? other.getOrderModel() == null : this.getOrderModel().equals(other.getOrderModel()))
            && (this.getFixedTime() == null ? other.getFixedTime() == null : this.getFixedTime().equals(other.getFixedTime()))
            && (this.getAdvanceDay() == null ? other.getAdvanceDay() == null : this.getAdvanceDay().equals(other.getAdvanceDay()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpu() == null) ? 0 : getSpu().hashCode());
        result = prime * result + ((getPdName() == null) ? 0 : getPdName().hashCode());
        result = prime * result + ((getWarehouseNo() == null) ? 0 : getWarehouseNo().hashCode());
        result = prime * result + ((getWarehouseName() == null) ? 0 : getWarehouseName().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getChannelType() == null) ? 0 : getChannelType().hashCode());
        result = prime * result + ((getDefaultSupplier() == null) ? 0 : getDefaultSupplier().hashCode());
        result = prime * result + ((getOrderModel() == null) ? 0 : getOrderModel().hashCode());
        result = prime * result + ((getFixedTime() == null) ? 0 : getFixedTime().hashCode());
        result = prime * result + ((getAdvanceDay() == null) ? 0 : getAdvanceDay().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spu=").append(spu);
        sb.append(", pdName=").append(pdName);
        sb.append(", warehouseNo=").append(warehouseNo);
        sb.append(", warehouseName=").append(warehouseName);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", channelType=").append(channelType);
        sb.append(", defaultSupplier=").append(defaultSupplier);
        sb.append(", orderModel=").append(orderModel);
        sb.append(", fixedTime=").append(fixedTime);
        sb.append(", advanceDay=").append(advanceDay);
        sb.append(", source=").append(source);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}