package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.service.supply.TmsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/tms")
public class TmsController {
    @Resource
    private TmsService tmsService;
    @GetMapping(value = "/tms")
    public  String tmsCutoff(@RequestParam(value = "order", required = false) String order,
                             @RequestParam(value = "complete", required = false) String complete)throws Exception{
        return tmsService.tmsCutoff(order,complete);
    }
}
