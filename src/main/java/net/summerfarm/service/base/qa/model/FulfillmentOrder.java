package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 履约单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-242 16:33:30
 */
@Getter
@Setter
@TableName("fulfillment_order")
public class FulfillmentOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 履约单号
     */
    @TableId(value = "fulfillment_no", type = IdType.AUTO)
    private Long fulfillmentNo;

    /**
     * 外部关联订单号
     */
    @TableField("out_order_no")
    private String outOrderNo;

    /**
     * 履约单来源，100：SaaS订单，200：鲜沐订单，300：运配外单
     */
    @TableField("order_source")
    private Integer orderSource;

    /**
     * 履约时间
     */
    @TableField("fulfillment_time")
    private LocalDate fulfillmentTime;

    /**
     * 履约方式，0：干配，1：自提，2：干线
     */
    @TableField("fulfillment_way")
    private Integer fulfillmentWay;

    /**
     * 履约单状态: 10待排线，20待拣货，30配送中，40配送完成，41配送完成但缺货，42拒收，43部分拒收，44已自提，50已取消，51已关闭，52已拦截
     */
    @TableField("fulfillment_status")
    private Integer fulfillmentStatus;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 履约单类型：10普通订单，11直发采购订单，20售后回收单，21售后补发单，22售后拒收单，30 样品订单，40外部订单
     */
    @TableField("fulfillment_type")
    private Integer fulfillmentType;

    /**
     * 历史字段，用于保存逆向订单关联的正向订单号
     */
    @TableField("remark")
    private String remark;

    /**
     * 外部客户ID 对应鲜沐m_id
     */
    @TableField("out_client_id")
    private String outClientId;

    /**
     * 外部客户名称
     */
    @TableField("out_client_name")
    private String outClientName;

    /**
     * 外部客户类型：10单店，11大客户
     */
    @TableField("out_client_type")
    private Integer outClientType;

    /**
     * 外部联系人ID  对应鲜沐contact_id
     */
    @TableField("out_contact_id")
    private String outContactId;

    /**
     * 省
     */
    @TableField("province")
    private String province;

    /**
     * 市
     */
    @TableField("city")
    private String city;

    /**
     * 区
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 外部客户手机
     */
    @TableField("out_client_phone")
    private String outClientPhone;

    /**
     * poi
     */
    @TableField("poi")
    private String poi;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * saas租户id，鲜沐订单默认1
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * saas订单类型 0:无仓 1: 代仓 2:自营仓
     */
    @TableField("saas_order_warehouse_type")
    private Integer saasOrderWarehouseType;

    /**
     * 履约单新状态 0待履约、1履约中、2履约完成、-1取消履约
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 售后履约状态 0未售后、1退款中
     */
    @TableField("after_sale_status")
    private Integer afterSaleStatus;

    /**
     * 售后金额
     */
    @TableField("after_sale_price")
    private BigDecimal afterSalePrice;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 期望送达时间
     */
    @TableField("expect_arrive_time")
    private LocalDateTime expectArriveTime;

    /**
     * 地址备注
     */
    @TableField("address_remark")
    private String addressRemark;

    /**
     * 子订单号（鲜沐订单记录delivery_plan_id）
     */
    @TableField("order_sub_no")
    private String orderSubNo;

    /**
     * 履约单备注
     */
    @TableField("fulfillment_remark")
    private String fulfillmentRemark;


}
