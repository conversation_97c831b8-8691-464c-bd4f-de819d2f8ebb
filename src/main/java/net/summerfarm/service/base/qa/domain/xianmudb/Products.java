package net.summerfarm.service.base.qa.domain.xianmudb;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@ApiModel(description = "商品实体类")
@Data
public class Products implements Serializable {

    @ApiModelProperty(value = "商品id")
    @TableId
    private Long pdId;

    @ApiModelProperty(value = "商品编号")
    private String pdNo;

    private Integer categoryId;

    @ApiModelProperty(value = "品牌id")
    private Integer brandId;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "上架时间")
    private Date createTime;

    @ApiModelProperty(value = "商品描述")
    private String pddetail;

    @ApiModelProperty(value = "详情图片")
    private String detailPicture;

    @ApiModelProperty(value = "从配送开始的售后时间")
    private Integer afterSaleTime;

    @ApiModelProperty(value = "售后类型")
    private String afterSaleType;

    /**
     * @deprecated 售后单位放到sku维度
     */
    @ApiModelProperty(value = "售后单位")
    @Deprecated
    private String afterSaleUnit;

    @ApiModelProperty(value = "产地")
    private Integer origin;

    @ApiModelProperty(value = "贮存方式")
    private String storageMethod;

    @ApiModelProperty(value = "商品介绍")
    private String slogan;

    private String otherSlogan;

    @ApiModelProperty(value = "首页缩略图")
    private String picturePath;

    @ApiModelProperty(value = "贮存区域")
    private Integer storageLocation;

    @ApiModelProperty(value = "城市编号")
    @TableField(exist = false)
    private Integer areaNo;

    @ApiModelProperty(value = "标记位-过时的spu  1代表过时，商品被删除")
    private Integer outdated;

    @ApiModelProperty(value = "商品介绍信息")
    private String productIntroduction;

    private String  refundType;

    @ApiModelProperty(value = "保质期时长")
    private Integer qualityTime;

    @ApiModelProperty(value = "保质期时长单位")
    private String qualityTimeUnit;

    /**
     * 保质期时长类型, 0 固定时长, 1 到期时间
     */
    @Getter
    @Setter
    private Integer qualityTimeType;

    @ApiModelProperty(value = "临保预警时长")
    private Integer warnTime;

    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 上新类型：0、平台 1、大客户 2、帆台上新
     */
    private Integer createType;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 上新审核状态：0、待上新 1、上新成功 2、上新失败
     */
    private Integer auditStatus;

    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 商品实物名
     */
    private String realName;

    /**
     * 操作人adminId
     */
    private Integer auditor;

}
