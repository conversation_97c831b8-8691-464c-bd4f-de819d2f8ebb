package net.summerfarm.service.base.qa.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AICaseExport
 * @date 2025-04-15
 */

@Document(indexName = "ai_case_export")
@Data
public class AICaseExport {
    @Id
    private String projectId;

    @Field
    private Object generatedCase;

    @Field
    private String selectedTestPoints;
    @Field
    private String operateTime;

    public AICaseExport() {
    }

    public AICaseExport(String projectId, Object generatedCase, String selectedTestPoints, String operateTime) {
        this.projectId = projectId;
        this.generatedCase = generatedCase;
        this.selectedTestPoints = selectedTestPoints;
        this.operateTime = operateTime;
    }
}
