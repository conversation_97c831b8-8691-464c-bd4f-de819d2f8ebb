package net.summerfarm.service.base.qa.DTO;

import lombok.Builder;
import lombok.Data;

/**
 * 类<code>Doc</code>用于：TODO
 * atp的请求对象
 * <AUTHOR>
 * @ClassName ATPRequestDto
 * @date 2025-02-11
 */
@Data
@Builder
public class ATPRequestDto {
    private String env;
    //dubbo接口名
    private String interfaceName;
    //dubbo方法名
    private String methodName;
    //dubbo方法参数类型
    private String[] paramTypes;
    //dubbo方法参数
    private Object[] params;

    public ATPRequestDto() {
    }
    public ATPRequestDto(String env, String interfaceName, String methodName, String[] paramTypes, Object[] params) {
        this.interfaceName = interfaceName;
        this.methodName = methodName;
        this.paramTypes = paramTypes;
        this.params = params;
        this.env = env;
    }
}
