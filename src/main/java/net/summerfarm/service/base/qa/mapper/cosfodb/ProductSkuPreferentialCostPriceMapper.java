package net.summerfarm.service.base.qa.mapper.cosfodb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.cosfodb.ProductSkuPreferentialCostPrice;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 省心定报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@DS("COFSO_DB")
@Mapper
public interface ProductSkuPreferentialCostPriceMapper extends BaseMapper<ProductSkuPreferentialCostPrice> {


}
