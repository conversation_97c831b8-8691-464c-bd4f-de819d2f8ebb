package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 供应商协同配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-144 15:13:42
 */
@Getter
@Setter
@TableName("supplier_coordination_config")
public class SupplierCoordinationConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Long supplierId;

    /**
     * 订单是否协同，0否；1是
     */
    @TableField("po_coordination_tab")
    private Boolean poCoordinationTab;

    /**
     * 入库是否协同，0否；1是
     */
    @TableField("inbound_coordination_tab")
    private Boolean inboundCoordinationTab;

    /**
     * 对账是否协同，0否；1是
     */
    @TableField("reconciliation_coordination_tab")
    private Boolean reconciliationCoordinationTab;

    /**
     * 发票是否协同，0否；1是
     */
    @TableField("invoice_coordination_tab")
    private Boolean invoiceCoordinationTab;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;


}
