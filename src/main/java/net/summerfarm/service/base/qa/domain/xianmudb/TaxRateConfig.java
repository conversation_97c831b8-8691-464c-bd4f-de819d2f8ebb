package net.summerfarm.service.base.qa.domain.xianmudb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "税率配置信息实体类")
@Data
public class TaxRateConfig {

    private Integer id;

    @ApiModelProperty(value = "类目id")
    private Integer categoryId;

    @ApiModelProperty(value = "pd_id")
    private Long pdId;

    @ApiModelProperty(value = "税率分类编码")
    private String taxRateCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRateValue;

    @ApiModelProperty(value = "修改人")
    private Integer updater;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "发起人")
    private Integer creator;

    @ApiModelProperty(value = "发起时间")
    private Date createTime;
}
