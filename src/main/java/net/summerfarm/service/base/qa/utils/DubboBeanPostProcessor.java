package net.summerfarm.service.base.qa.utils;

import org.apache.dubbo.config.AbstractConfig;
import org.apache.dubbo.rpc.model.ApplicationModel;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName DubboBeanPostProcessor
 * @date 2025-02-17
 */
public class DubboBeanPostProcessor implements BeanPostProcessor {
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof AbstractConfig) {
            AbstractConfig abstractConfig = (AbstractConfig) bean;
            ApplicationModel.getConfigManager().addConfig(abstractConfig);
        }
        return BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
    }
}
