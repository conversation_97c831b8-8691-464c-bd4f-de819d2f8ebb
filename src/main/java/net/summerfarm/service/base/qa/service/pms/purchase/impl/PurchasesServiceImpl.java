package net.summerfarm.service.base.qa.service.pms.purchase.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.controller.pms.purchase.input.PurchaseCreateInput;
import net.summerfarm.service.base.qa.domain.PmsSupplyList;
import net.summerfarm.service.base.qa.domain.StockTaskStorage;
import net.summerfarm.service.base.qa.mapper.PmsSupplyListMapper;
import net.summerfarm.service.base.qa.mapper.StockTaskStorageMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsWarehouseConfigMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsContainerMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsCabinetMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.WmsMissionSourcePropertyMapper;
import net.summerfarm.service.base.qa.model.WmsWarehouseConfig;
import net.summerfarm.service.base.qa.model.WmsCabinet;
import net.summerfarm.service.base.qa.model.WmsMissionSourceProperty;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Calendar;

@Service
@Slf4j
public class PurchasesServiceImpl implements PurchasesService {
    private final OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    @Resource
    private TokenService tokenService;

    @Resource
    private PmsSupplyListMapper pmsSupplyListMapper;

    @Resource
    private StockTaskStorageMapper stockTaskStorageMapper;

    @Resource
    private WmsWarehouseConfigMapper wmsWarehouseConfigMapper;

    @Resource
    private WmsCabinetMapper wmsCabinetMapper;

    @Resource
    private WmsMissionSourcePropertyMapper wmsMissionSourcePropertyMapper;

    @Override
    public String purchaseCreate(PurchaseCreateInput input)
    {
        String baseUrl = tokenService.getBaseDomain(input.getEnvFlag());
        long timestamp = System.currentTimeMillis();
        if (input.getRemark() == null || input.getRemark().trim().isEmpty()) {
            input.setRemark("造数-采购下单" + timestamp);
        }
        String token = tokenService.getXmAdminToken(input.getEnvFlag(),  "<EMAIL>", "123456");
        //获取商品信息
        String skuInfoUrl = baseUrl + "/goods-center/goods/query/sku/page";
        JSONObject skuInfoData = new JSONObject();
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        skuInfoData.put("pageIndex", 1);
        skuInfoData.put("pageSize", 20);
        skuInfoData.put("skuList", input.getSkuList());
        String skuInfoResult = okHttpUtils.sendCustomJsonPost(skuInfoUrl, skuInfoData.toString(), headers);
        JSONArray skuInfo = JSONObject.parseObject(skuInfoResult).getJSONObject("data").getJSONArray("list");
        if (skuInfo.isEmpty() || skuInfoResult == null) {
            throw  new BizException("商品数据不存在");
        }
        log.info("采购造数流程--获取商品信息：{}", skuInfo);

        //获取用户信息
        String userUrl = baseUrl + "/authentication/auth/personInfo";
        String userResult = okHttpUtils.sendCustomJsonPost(userUrl, "", headers);
        JSONObject userInfo = JSONObject.parseObject(userResult).getJSONObject("data");
        if (userInfo.isEmpty()){
            throw new BizException("用户数据不存在");
        }
        log.info("采购造数流程--获取用户信息：{}", userInfo);

        //获取供应商信息
        String supplierUrl = baseUrl + "/pms-service/supplier/query/detail";
        JSONObject supplierData = new JSONObject();
        supplierData.put("id", input.getSupplierNo());
        String supplierResult = okHttpUtils.sendCustomJsonPost(supplierUrl,supplierData.toString() , headers);
        JSONObject supplierInfo = JSONObject.parseObject(supplierResult).getJSONObject("data");
        if (supplierInfo.isEmpty()){
            throw new BizException("供应商数据不存在");
        }
        log.info("采购造数流程--获取供应商信息：{}", supplierInfo);

        //设置供货协同配置
        String supplierCoordinationUrl = baseUrl + "/pms-service/supplier/upsert/update-coordination";
        JSONObject supplierCoordinationData = new JSONObject();
        supplierCoordinationData.put("supplierId", input.getSupplierNo());
        supplierCoordinationData.put("poCoordinationTab", 0);
        supplierCoordinationData.put("inboundCoordinationTab", 0);
        supplierCoordinationData.put("reconciliationCoordinationTab", 0);
        supplierCoordinationData.put("invoiceCoordinationTab", 0);
        okHttpUtils.sendCustomJsonPost(supplierCoordinationUrl,supplierCoordinationData.toString() , headers);

        //获取仓库信息
        String warehouseUrl = baseUrl + "/summerfarm-wnc/warehouse-storage/query/xm-warehouse-detail";
        JSONObject warehouseData = new JSONObject();
        warehouseData.put("warehouseNo", input.getWarehouseNo());
        String warehouseResult = okHttpUtils.sendCustomJsonPost(warehouseUrl,warehouseData.toString() , headers);
        JSONObject warehouseInfo = JSONObject.parseObject(warehouseResult).getJSONObject("data");
        if (warehouseInfo.isEmpty()){
            throw new BizException("仓库数据不存在");
        }
        log.info("采购造数流程--获取仓库信息：{}", warehouseInfo);
        //设置供货目录
        List<String> missingSpuNos = supplyConfig(input,skuInfo);
        if (!missingSpuNos.isEmpty()){
            log.info("采购造数流程--设置供货目录："+missingSpuNos);
            //获取对应的spu和商品名称
            Map<String, String> spuToPdNameMap = skuInfo.stream()
                    .map(obj -> (JSONObject) obj)
                    .filter(json -> missingSpuNos.contains(json.getString("spu")))
                    .collect(Collectors.groupingBy(
                            json -> json.getString("spu"),
                            Collectors.mapping(
                                    json -> json.getString("title"),
                                    Collectors.reducing((a, b) -> a)
                            )
                    ))
                    .entrySet().stream()
                    .filter(e -> e.getValue().isPresent())
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));

            String supplyConfigUrl = baseUrl + "/pms-service/supply-list/upsert/add";
            for (String missingSpuNo : missingSpuNos){
                JSONObject  supplyConfigData = new JSONObject();
                supplyConfigData.put("spu", missingSpuNo);
                supplyConfigData.put("pdName", spuToPdNameMap.get(missingSpuNo));
                supplyConfigData.put("warehouseNo", input.getWarehouseNo());
                supplyConfigData.put("warehouseName", warehouseInfo.getString("warehouseName"));
                supplyConfigData.put("supplierId", input.getSupplierNo());
                supplyConfigData.put("supplierName", supplierInfo.getString("name"));
                supplyConfigData.put("channelType", 1);
                supplyConfigData.put("orderModel", 1);
                supplyConfigData.put("advanceDay", 3);
                supplyConfigData.put("defaultSupplier", 0);
                String supplyConfigResult = okHttpUtils.sendCustomJsonPost(supplyConfigUrl,supplyConfigData.toString(), headers);
                System.out.println(supplyConfigResult);
                if (!JSONObject.parseObject(supplyConfigResult).containsValue("请求成功")){
                    throw new BizException("设置供货目录失败");
                }
            }
        }

        //新增采购单
        //构建采购单参数
        String purchaseCreateData = purchaseCreateVo(skuInfo,supplierInfo,userInfo,warehouseInfo,input);
        String purchaseCreateUrl = baseUrl + "/pms-service/po/purchases/upsert/release";
        String purchaseCreateResult = okHttpUtils.sendCustomJsonPost(purchaseCreateUrl,purchaseCreateData, headers);
        JSONObject  purchaseCreateJson = JSONObject.parseObject(purchaseCreateResult);
        if (!purchaseCreateJson.containsValue("请求成功")){
            throw new BizException("采购单创建失败");
        }
        String purchaseNo = purchaseCreateJson.getString("data");
        System.out.println(purchaseCreateResult);

        //操作入库
        if  (input.getFlag() == 3){
            try {
                executeInboundProcess(purchaseNo, input, userInfo, headers, baseUrl);
                log.info("采购单入库操作完成：{}", purchaseNo);
            } catch (Exception e) {
                log.error("采购单入库操作失败：{}, 错误信息：{}", purchaseNo, e.getMessage());
                throw new BizException("采购单入库操作失败：" + e.getMessage());
            }
        }

        return purchaseNo;
    }

    //构建采购单新增所需参数
    public String purchaseCreateVo(JSONArray skuInfoList,JSONObject supplierInfo,JSONObject userInfo,JSONObject warehouseInfo,PurchaseCreateInput input)
    {
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        JSONObject purchaseCreateVo = new JSONObject();
        JSONArray purchasePlanList =  new JSONArray();
        purchaseCreateVo.put("warehouseNo",warehouseInfo.getIntValue("warehouseNo"));
        purchaseCreateVo.put("purchaser",userInfo.getString("realName"));
        purchaseCreateVo.put("remark",input.getRemark());
        purchaseCreateVo.put("purchaseTime",today);
        purchaseCreateVo.put("receiver",warehouseInfo.getString("manageAdminName"));
        purchaseCreateVo.put("purchasesType",0);
        if(input.getFlag() != 1){
            purchaseCreateVo.put("arrangeTime",today);
        }
        purchaseCreateVo.put("deliveryTime",today);

        for (int i = 0; i < skuInfoList.size(); i++) {
            JSONObject skuItem = skuInfoList.getJSONObject(i);
            JSONObject purchasePlan = new JSONObject();
            purchasePlan.put("title",skuItem.getString("title"));
            purchasePlan.put("specification",skuItem.getString("specification"));
            purchasePlan.put("sku",skuItem.getString("sku"));
            purchasePlan.put("spu",skuItem.getString("spu"));
            purchasePlan.put("price",input.getQuantity());
            purchasePlan.put("unitPrice",1);
            purchasePlan.put("quantity",input.getQuantity());
            purchasePlan.put("specificationUnit",skuItem.getString("specificationUnit"));
            purchasePlan.put("supplier",supplierInfo.getString("name"));
            purchasePlan.put("supplierId",input.getSupplierNo());
            purchasePlan.put("priceType",0);
            purchasePlan.put("latestArrivalDate",today);
            purchasePlan.put("netWeight",input.getQuantity());
            purchasePlanList.add(purchasePlan);
        }
        purchaseCreateVo.put("purchasePlanList",purchasePlanList);
        return purchaseCreateVo.toString();
    }

    //设置供货目录
    public List<String> supplyConfig(PurchaseCreateInput  input,JSONArray skuList) {
        if (skuList == null || skuList.isEmpty()) {
            throw new BizException("SKU 列表不能为空");
        }
        List<String> spuNoList = skuList.stream()
                .map(obj -> ((JSONObject) obj).getString("spu")) // 提取每个对象的 spu
                .filter(spuNo -> spuNo != null && !spuNo.trim().isEmpty()) // 过滤空值
                .distinct() // 去重
                .collect(Collectors.toList());
        // 2. 查询数据库中已存在的 PmsSupplyList 记录
        LambdaQueryWrapper<PmsSupplyList> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PmsSupplyList::getSpu, spuNoList); // 根据 spu 字段进行筛选
        queryWrapper.eq(PmsSupplyList::getSupplierId, input.getSupplierNo());
        queryWrapper.eq(PmsSupplyList::getWarehouseNo, input.getWarehouseNo());
        List<PmsSupplyList> existingList = pmsSupplyListMapper.selectList(queryWrapper);

        // 3. 提取已存在的 spuNo 集合用于比对
        Set<String> existingSkuNos = Optional.ofNullable(existingList).orElse(Collections.emptyList()).stream()
                .map(PmsSupplyList::getSpu)
                .collect(Collectors.toSet());

        // 4. 找出缺失的 spuNo
        return spuNoList.stream()
                .filter(spuNo -> !existingSkuNos.contains(spuNo))
                .collect(Collectors.toList());
    }

    public Long getStockTask(String sourceNo, String type)
    {
        int maxRetries = 5;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                log.info("查询入库任务，采购单：{}，类型：{}，第{}次尝试", sourceNo, type, retryCount + 1);

                LambdaQueryWrapper<StockTaskStorage> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(StockTaskStorage::getSourceId, sourceNo);
                queryWrapper.eq(StockTaskStorage::getType, type);
                queryWrapper.eq(StockTaskStorage::getState, 0);
                StockTaskStorage stockTaskInfo = stockTaskStorageMapper.selectOne(queryWrapper);

                if (stockTaskInfo != null) {
                    log.info("成功找到入库任务，采购单：{}，任务ID：{}", sourceNo, stockTaskInfo.getId());
                    return stockTaskInfo.getId();
                }

                retryCount++;
                if (retryCount < maxRetries) {
                    log.warn("未找到入库任务，采购单：{}，第{}次尝试失败，1秒后重试", sourceNo, retryCount);
                    Thread.sleep(1000); // 等待1秒
                } else {
                    log.error("重试{}次后仍未找到入库任务，采购单：{}", maxRetries, sourceNo);
                    throw new BizException("未找到对应入库任务：" + sourceNo + "，已重试" + maxRetries + "次");
                }

            } catch (InterruptedException e) {
                log.error("查询入库任务时线程被中断，采购单：{}", sourceNo);
                Thread.currentThread().interrupt();
                throw new BizException("查询入库任务被中断：" + sourceNo);
            } catch (Exception e) {
                if (e instanceof BizException) {
                    throw e;
                }
                log.error("查询入库任务异常，采购单：{}，错误信息：{}", sourceNo, e.getMessage());
                throw new BizException("查询入库任务异常：" + e.getMessage());
            }
        }

        // 这行代码理论上不会执行到，但为了代码完整性保留
        throw new BizException("未找到对应入库任务：" + sourceNo);
    }

    /**
     * 执行入库流程
     * @param purchaseNo 采购单号
     * @param input 输入参数
     * @param userInfo 用户信息
     * @param headers 请求头
     * @param baseUrl 基础URL
     */
    private void executeInboundProcess(String purchaseNo, PurchaseCreateInput input,
                                       JSONObject userInfo,
                                     Map<String, String> headers, String baseUrl) {
        // 获取入库任务ID
        Long stockTaskId = getStockTask(purchaseNo, "11");
        log.info("采购单{}对应的入库任务ID：{}", purchaseNo, stockTaskId);

        // 检查仓库是否开启精细化配置
        boolean isFineGrainedWarehouse = checkWarehouseFineGrainedConfig(input.getWarehouseNo());
        log.info("仓库{}精细化配置状态：{}", input.getWarehouseNo(), isFineGrainedWarehouse ? "已开启" : "未开启");

        if (isFineGrainedWarehouse) {
            // 精细化仓库入库流程
            executeFineGrainedInbound(stockTaskId, purchaseNo, input,  userInfo, headers, baseUrl);

        } else {
            // 普通仓库入库流程
            executeNormalInbound(stockTaskId, purchaseNo, input, userInfo, headers, baseUrl);
        }
    }

    /**
     * 检查仓库精细化配置
     * @param warehouseNo 仓库编号
     * @return 是否开启精细化配置
     */
    private boolean checkWarehouseFineGrainedConfig(Integer warehouseNo) {
        try {
            LambdaQueryWrapper<WmsWarehouseConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmsWarehouseConfig::getWarehouseNo, warehouseNo)
                       .eq(WmsWarehouseConfig::getConfigKey, "cabinetStatus")
                       .eq(WmsWarehouseConfig::getConfigValue, "1");

            List<WmsWarehouseConfig> configs = wmsWarehouseConfigMapper.selectList(queryWrapper);
            return !configs.isEmpty();
        } catch (Exception e) {
            log.error("查询仓库精细化配置失败，仓库编号：{}，错误信息：{}", warehouseNo, e.getMessage());
            return false;
        }
    }

    /**
     * 执行普通入库操作
     */
    private void executeNormalInbound(Long stockTaskId, String purchaseNo, PurchaseCreateInput input,
                                     JSONObject userInfo,
                                     Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始执行普通入库操作，采购单：{}", purchaseNo);

            // 构建入库明细详情
            JSONArray orderDetails = buildInboundOrderDetails(stockTaskId, purchaseNo, null, headers, baseUrl);

            // 构建入库主体请求参数
            JSONObject inboundRequest = new JSONObject();
            inboundRequest.put("warehouseNo", input.getWarehouseNo());
            inboundRequest.put("stockStorageTaskId", stockTaskId);
            inboundRequest.put("type", 0);
            inboundRequest.put("operator", userInfo.getIntValue("id"));
            inboundRequest.put("bizId", purchaseNo);
            inboundRequest.put("orderDetails", orderDetails);

            // 调用入库接口
            String inboundUrl = baseUrl + "/summerfarm-wms/inbound/create";
            String inboundResult = okHttpUtils.sendCustomJsonPost(inboundUrl, inboundRequest.toString(), headers);
            log.info("普通入库操作结果：{}", inboundResult);

            JSONObject resultJson = JSONObject.parseObject(inboundResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("普通入库操作失败：" + inboundResult);
            }

        } catch (Exception e) {
            log.error("普通入库操作失败，采购单：{}，错误信息：{}", purchaseNo, e.getMessage());
            throw new BizException("普通入库操作失败：" + e.getMessage());
        }
    }

    /**
     * 执行精细化入库操作
     */
    private void executeFineGrainedInbound(Long stockTaskId, String purchaseNo, PurchaseCreateInput input,
                                          JSONObject userInfo,
                                          Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始执行精细化入库操作，采购单：{}", purchaseNo);

            // 1. 确认收货
            confirmArrivalCargo(stockTaskId, input.getWarehouseNo(), userInfo.getIntValue("id"), headers, baseUrl);

            // 2. 获取容器
            String containerCode = getAvailableContainer(input.getWarehouseNo(), headers, baseUrl);

            // 3. 构建入库单详情（包含容器信息）
            JSONArray orderDetails = buildInboundOrderDetails(stockTaskId, purchaseNo, containerCode, headers, baseUrl);

            // 4. 创建入库单
            JSONObject inboundRequest = new JSONObject();
            inboundRequest.put("warehouseNo", input.getWarehouseNo());
            inboundRequest.put("stockStorageTaskId", stockTaskId);
            inboundRequest.put("type", 0);
            inboundRequest.put("operator", userInfo.getIntValue("id"));
            inboundRequest.put("bizId", purchaseNo);
            inboundRequest.put("orderDetails", orderDetails);
            inboundRequest.put("autoShelving", false);

            String inboundUrl = baseUrl + "/summerfarm-wms/inbound/create";
            String inboundResult = okHttpUtils.sendCustomJsonPost(inboundUrl, inboundRequest.toString(), headers);
            log.info("精细化入库操作结果：{}", inboundResult);

            JSONObject resultJson = JSONObject.parseObject(inboundResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("精细化入库操作失败：" + inboundResult);
            }

            // 5.执行上架操作
            executeShelving(orderDetails,purchaseNo, input.getWarehouseNo(), containerCode, userInfo, headers, baseUrl);

        } catch (Exception e) {
            log.error("精细化入库操作失败，采购单：{}，错误信息：{}", purchaseNo, e.getMessage());
            throw new BizException("精细化入库操作失败：" + e.getMessage());
        }
    }

    /**
     * 确认收货
     * @param stockTaskId 入库任务ID
     * @param warehouseNo 仓库编号
     * @param operatorId 操作员ID
     * @param headers 请求头
     * @param baseUrl 基础URL
     */
    private void confirmArrivalCargo(Long stockTaskId, Integer warehouseNo, Integer operatorId,
                                   Map<String, String> headers, String baseUrl) {
        try {
            log.info("入库操作--开始确认收货，入库任务ID：{}", stockTaskId);

            JSONObject arrivalRequest = new JSONObject();
            arrivalRequest.put("stockStorageTaskId", stockTaskId);
            arrivalRequest.put("warehouseNo", warehouseNo);
            arrivalRequest.put("operator", operatorId);

            String arrivalUrl = baseUrl + "/summerfarm-wms/in-store/upsert/arrival_cargo";
            String arrivalResult = okHttpUtils.sendCustomJsonPost(arrivalUrl, arrivalRequest.toString(), headers);
            log.info("确认收货结果：{}", arrivalResult);

            JSONObject resultJson = JSONObject.parseObject(arrivalResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("入库操作--确认收货失败：" + arrivalResult);
            }

        } catch (Exception e) {
            log.error("确认收货失败，入库任务ID：{}，错误信息：{}", stockTaskId, e.getMessage());
            throw new BizException("确认收货失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用容器
     * @param warehouseNo 仓库编号
     * @param headers 请求头
     * @param baseUrl 基础URL
     * @return 容器编码
     */
    private String getAvailableContainer(Integer warehouseNo, Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始获取可用容器，仓库编号：{}", warehouseNo);

            // 先尝试通过HTTP接口获取容器
            JSONObject containerRequest = new JSONObject();
            containerRequest.put("pageIndex", 1);
            containerRequest.put("pageSize", 10);
            containerRequest.put("warehouseNo", warehouseNo);
            containerRequest.put("purpose", 5);
            containerRequest.put("containerStatus", "1");
            containerRequest.put("occupyStatus", "0");

            String containerUrl = baseUrl + "/summerfarm-wms/container/query/page";
            String containerResult = okHttpUtils.sendCustomJsonPost(containerUrl, containerRequest.toString(), headers);
            log.info("容器查询结果：{}", containerResult);

            JSONObject resultJson = JSONObject.parseObject(containerResult);
            if (resultJson.containsKey("data")) {
                JSONObject dataObj = resultJson.getJSONObject("data");
                if (dataObj.containsKey("list")) {
                    JSONArray containerList = dataObj.getJSONArray("list");
                    if (!containerList.isEmpty()) {
                        JSONObject container = containerList.getJSONObject(0);
                        String containerCode = container.getString("containerCode");
                        log.info("通过HTTP接口获取到容器：{}", containerCode);
                        return containerCode;
                    }
                }
            }
            throw new BizException("入库操作--获取容器失败，仓库编号：{}，错误信息：{}",warehouseNo, resultJson.getString("msg"));

        } catch (Exception e) {
            log.error("入库操作--获取容器失败，仓库编号：{}，错误信息：{}", warehouseNo, e.getMessage());
            throw new BizException("入库操作--获取容器失败：" + e.getMessage());
        }
    }



    /**
     * 构建入库单详情
     * @param stockTaskId 入库任务ID
     * @param purchaseNo 采购单号
     * @param containerCode 容器编码（精细化仓库需要）
     * @param headers 请求头
     * @param baseUrl 基础URL
     * @return 入库单详情数组
     */
    private JSONArray buildInboundOrderDetails(Long stockTaskId, String purchaseNo, String containerCode,
                                             Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始构建入库单详情，入库任务ID：{}", stockTaskId);

            // 调用入库任务详情接口获取数据
            JSONObject detailRequest = new JSONObject();
            detailRequest.put("taskId", stockTaskId);
            detailRequest.put("queryType", 0);

            String detailUrl = baseUrl + "/summerfarm-wms/in-store/query/detail";
            String detailResult = okHttpUtils.sendCustomJsonPost(detailUrl, detailRequest.toString(), headers);
            log.info("入库任务详情查询结果：{}", detailResult);

            JSONObject resultJson = JSONObject.parseObject(detailResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("查询入库任务详情失败：" + detailResult);
            }

            JSONObject dataObj = resultJson.getJSONObject("data");
            JSONArray resDTOList = dataObj.getJSONArray("resDTOList");

            if (resDTOList == null || resDTOList.isEmpty()) {
                throw new BizException("入库任务详情为空，任务ID：" + stockTaskId);
            }

            JSONArray orderDetails = new JSONArray();

            // 遍历resDTOList构建入库单详情
            for (int i = 0; i < resDTOList.size(); i++) {
                JSONObject resDTO = resDTOList.getJSONObject(i);
                JSONObject orderDetail = new JSONObject();

                // 从接口返回数据中提取信息
                orderDetail.put("sku", resDTO.getString("sku"));
                orderDetail.put("pdName", resDTO.getString("pdName"));
                orderDetail.put("temperature", resDTO.getIntValue("storageLocationId"));
                orderDetail.put("packaging", resDTO.getString("packaging"));
                orderDetail.put("specification", resDTO.getString("weight"));
                orderDetail.put("purchaseNo", purchaseNo);
                orderDetail.put("stockNum", resDTO.getIntValue("quantity")-resDTO.getIntValue("actualQuantity"));
                orderDetail.put("categoryType", resDTO.getIntValue("categoryType"));
                orderDetail.put("supplier", resDTO.getString("supplierName"));
                orderDetail.put("supplierId", resDTO.getIntValue("supplierId"));
                orderDetail.put("cargoInspection", 0);//0表示不需要质检
                orderDetail.put("shouldIn", resDTO.getIntValue("quantity"));

                // 获取当天日期和365天后的日期时间戳
                Date currentDate = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(currentDate);
                calendar.add(Calendar.DAY_OF_YEAR, 365);

                orderDetail.put("produceAt", currentDate.getTime());
                orderDetail.put("shelfLife", calendar.getTime().getTime());
                orderDetail.put("belongType", resDTO.getIntValue("skuType"));

                // 如果是精细化仓库，添加容器信息
                if (containerCode != null && !containerCode.trim().isEmpty()) {
                    orderDetail.put("receivingContainer", containerCode);
                }

                orderDetails.add(orderDetail);
            }

            log.info("构建入库单详情完成，共{}个商品", orderDetails.size());
            return orderDetails;

        } catch (Exception e) {
            log.error("构建入库单详情失败，入库任务ID：{}，错误信息：{}", stockTaskId, e.getMessage());
            throw new BizException("构建入库单详情失败：" + e.getMessage());
        }
    }

    /**
     * 执行上架操作
     * @param purchaseNo 采购单号
     * @param warehouseNo 仓库编号
     * @param containerCode 容器编码
     * @param userInfo 用户信息
     * @param headers 请求头
     * @param baseUrl 基础URL
     */
    private void executeShelving(JSONArray orderDetails,String purchaseNo, Integer warehouseNo, String containerCode,
                               JSONObject userInfo , Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始执行上架操作，采购单：{}", purchaseNo);

            // 1. 获取上架任务
            String missionNo = getShelvingMission(purchaseNo);

            // 2. 分配操作员
            assignOperator(missionNo, warehouseNo, userInfo, headers, baseUrl);

            // 3. 获取库位信息
            String cabinetCode = getAvailableCabinet(warehouseNo);

            // 4. 执行上架提交
            commitShelving(orderDetails,missionNo, warehouseNo, containerCode, cabinetCode, headers, baseUrl);

            log.info("上架操作完成，采购单：{}", purchaseNo);

        } catch (Exception e) {
            log.error("上架操作失败，采购单：{}，错误信息：{}", purchaseNo, e.getMessage());
            throw new BizException("上架操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取上架任务编号
     * @param purchaseNo 采购单号
     * @return 任务编号
     */
    private String getShelvingMission(String purchaseNo) {
        try {
            // 添加重试机制，等待上架任务生成
            int maxRetries = 5;
            int retryCount = 0;

            while (retryCount < maxRetries) {
                log.info("查询上架任务，采购单：{}，第{}次尝试", purchaseNo, retryCount + 1);

                LambdaQueryWrapper<WmsMissionSourceProperty> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WmsMissionSourceProperty::getSourceOrderNo, purchaseNo);
                List<WmsMissionSourceProperty> missions = wmsMissionSourcePropertyMapper.selectList(queryWrapper);

                if (!missions.isEmpty()) {
                    String missionNo = missions.get(0).getMissionNo();
                    log.info("成功找到上架任务，采购单：{}，任务编号：{}", purchaseNo, missionNo);
                    return missionNo;
                }

                retryCount++;
                if (retryCount < maxRetries) {
                    log.warn("未找到上架任务，采购单：{}，第{}次尝试失败，1秒后重试", purchaseNo, retryCount);
                    Thread.sleep(1000);
                } else {
                    throw new BizException("未找到上架任务，采购单：" + purchaseNo + "，已重试" + maxRetries + "次");
                }
            }

            throw new BizException("未找到上架任务：" + purchaseNo);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException("查询上架任务被中断：" + purchaseNo);
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            }
            log.error("查询上架任务异常，采购单：{}，错误信息：{}", purchaseNo, e.getMessage());
            throw new BizException("查询上架任务异常：" + e.getMessage());
        }
    }

    /**
     * 分配操作员
     * @param missionNo 任务编号
     * @param warehouseNo 仓库编号
     * @param userInfo 用户信息
     * @param headers 请求头
     * @param baseUrl 基础URL
     */
    private void assignOperator(String missionNo, Integer warehouseNo, JSONObject userInfo,
                              Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始分配操作员，任务编号：{}，操作员ID：{}", missionNo, userInfo.getString("id"));

            JSONObject assignRequest = new JSONObject();
            JSONArray operators = new JSONArray();
            JSONObject operator = new JSONObject();
            operator.put("operatorId", userInfo.getIntValue("id"));
            operator.put("operatorName", userInfo.getString("realName"));
            operators.add(operator);

            assignRequest.put("operators", operators);
            assignRequest.put("missionNo", missionNo);
            assignRequest.put("warehouseNo", warehouseNo);

            String assignUrl = baseUrl + "/summerfarm-wms/mission/upsert/assign";
            String assignResult = okHttpUtils.sendCustomJsonPost(assignUrl, assignRequest.toString(), headers);
            log.info("分配操作员结果：{}", assignResult);

            JSONObject resultJson = JSONObject.parseObject(assignResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("分配操作员失败：" + assignResult);
            }

        } catch (Exception e) {
            log.error("分配操作员失败，任务编号：{}，错误信息：{}", missionNo, e.getMessage());
            throw new BizException("分配操作员失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用库位
     * @param warehouseNo 仓库编号
     * @return 库位编码
     */
    private String getAvailableCabinet(Integer warehouseNo) {
        try {
            log.info("开始获取可用库位，仓库编号：{}", warehouseNo);

            LambdaQueryWrapper<WmsCabinet> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmsCabinet::getWarehouseNo, warehouseNo)
                       .eq(WmsCabinet::getCabinetStatus, 1)
                       .eq(WmsCabinet::getInitOption, 0)
                       .eq(WmsCabinet::getAllowMixedBatch, 1)
                       .eq(WmsCabinet::getAllowMixedPeriod, 1)
                       .eq(WmsCabinet::getAllowMixedSku, 1)
                       .eq(WmsCabinet::getAllowMixedSkuQuantity, 9999)
                       .eq(WmsCabinet::getAllowMixedPeriodQuantity, 9999)
                       .eq(WmsCabinet::getAllowMixedBatchQuantity, 9999)
                       .orderByDesc(WmsCabinet::getId);

            List<WmsCabinet> cabinets = wmsCabinetMapper.selectList(queryWrapper);
            if (cabinets.isEmpty()) {
                throw new BizException("未找到可用库位，仓库编号：" + warehouseNo);
            }

            String cabinetCode = cabinets.get(0).getCabinetCode();
            log.info("成功获取库位，仓库编号：{}，库位编码：{}", warehouseNo, cabinetCode);
            return cabinetCode;

        } catch (Exception e) {
            log.error("获取库位失败，仓库编号：{}，错误信息：{}", warehouseNo, e.getMessage());
            throw new BizException("获取库位失败：" + e.getMessage());
        }
    }

    /**
     * 执行上架提交
     * @param missionNo 任务编号
     * @param warehouseNo 仓库编号
     * @param containerCode 容器编码
     * @param cabinetCode 库位编码
     * @param headers 请求头
     * @param baseUrl 基础URL
     */
    private void commitShelving(JSONArray orderDetails, String missionNo, Integer warehouseNo, String containerCode,
                              String cabinetCode , Map<String, String> headers, String baseUrl) {
        try {
            log.info("开始执行上架提交，任务编号：{}，库位编码：{}", missionNo, cabinetCode);

            JSONObject commitRequest = new JSONObject();
            commitRequest.put("missionNo", missionNo);
            commitRequest.put("warehouseNo", warehouseNo.toString());

            JSONArray commitInfoArray = new JSONArray();

            // 获取当天日期和365天后的日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date currentDate = new Date();
            String todayDate = sdf.format(currentDate);

            // 计算365天后的日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_YEAR, 365);
            String shelfLifeDate = sdf.format(calendar.getTime());

            log.info("上架操作--生产日期：{}，保质期：{}", todayDate, shelfLifeDate);

            // 遍历入库单详情构建上架信息
            for (int i = 0; i < orderDetails.size(); i++) {
                JSONObject orderDetail = orderDetails.getJSONObject(i);
                JSONObject commitInfo = new JSONObject();

                commitInfo.put("sourceContainerNo", containerCode);
                commitInfo.put("produceTime", todayDate);
                commitInfo.put("targetCabinetNo", cabinetCode);
                commitInfo.put("dealNum", orderDetail.getIntValue("stockNum"));
                commitInfo.put("sku", orderDetail.getString("sku"));
                commitInfo.put("shelfLife", shelfLifeDate);

                commitInfoArray.add(commitInfo);
            }

            commitRequest.put("commitInfo", commitInfoArray);

            String commitUrl = baseUrl + "/summerfarm-wms/shelving/upsert/commit";
            String commitResult = okHttpUtils.sendCustomJsonPost(commitUrl, commitRequest.toString(), headers);
            log.info("上架提交结果：{}", commitResult);

            JSONObject resultJson = JSONObject.parseObject(commitResult);
            if (!resultJson.containsValue("请求成功")) {
                throw new BizException("上架提交失败：" + commitResult);
            }

        } catch (Exception e) {
            log.error("上架提交失败，任务编号：{}，错误信息：{}", missionNo, e.getMessage());
            throw new BizException("上架提交失败：" + e.getMessage());
        }
    }
}
