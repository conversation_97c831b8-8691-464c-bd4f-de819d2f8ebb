package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配送批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-242 18:35:56
 */
@Getter
@Setter
@TableName("tms_delivery_batch")
public class TmsDeliveryBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 类型，-1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车、6提货用车
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 任务状态 10待排线,20待拣货,30配送中,40配送完成 50关闭
     */
    @TableField("`status`")
    private Integer status;

    @TableField("car_id")
    private Long carId;

    /**
     * 司机id
     */
    @TableField("driver_id")
    private Long driverId;

    /**
     * 承运商id
     */
    @TableField("carrier_id")
    private Long carrierId;

    /**
     * 线路ID
     */
    @TableField("path_id")
    private Long pathId;

    /**
     * 线路编码
     */
    @TableField("path_code")
    private String pathCode;

    /**
     * 线路名称
     */
    @TableField("path_name")
    private String pathName;

    /**
     * 开始点位
     */
    @TableField("begin_site_id")
    private Long beginSiteId;

    /**
     * 结束点位
     */
    @TableField("end_site_id")
    private Long endSiteId;

    /**
     * 配送时间,履约交付时间
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 计划开始运输时间
     */
    @TableField("plan_begin_time")
    private LocalDateTime planBeginTime;

    /**
     * 总距离 km
     */
    @TableField("plan_total_distance")
    private BigDecimal planTotalDistance;

    /**
     * 实际总距离 km
     */
    @TableField("real_total_distance")
    private BigDecimal realTotalDistance;

    /**
     * 完成排线时间
     */
    @TableField("be_path_time")
    private LocalDateTime bePathTime;

    /**
     * 完成捡货时间
     */
    @TableField("pick_up_time")
    private LocalDateTime pickUpTime;

    /**
     * 完成配送时间
     */
    @TableField("finish_delivery_time")
    private LocalDateTime finishDeliveryTime;

    /**
     * 批次维度统计数据
     */
    @TableField("static_info")
    private String staticInfo;

    /**
     * 附属信息
     */
    @TableField("query_feature")
    private String queryFeature;

    /**
     * 创建人id
     */
    @TableField("create_id")
    private Integer createId;

    /**
     * 关闭原因
     */
    @TableField("close_reason")
    private String closeReason;

    /**
     * 预估运费
     */
    @TableField("estimate_fare")
    private BigDecimal estimateFare;

    /**
     * 智能排线总距离
     */
    @TableField("intelligence_total_distance")
    private BigDecimal intelligenceTotalDistance;

    /**
     * 区域
     */
    @TableField("area")
    private String area;

    /**
     * 班次 0正常 1加班
     */
    @TableField("classes")
    private Integer classes;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 关闭人
     */
    @TableField("close_user")
    private String closeUser;

    /**
     * 体积装载率
     */
    @TableField("volume_load_ratio")
    private BigDecimal volumeLoadRatio;

    /**
     * 重量装载率
     */
    @TableField("weight_load_ratio")
    private BigDecimal weightLoadRatio;

    /**
     * 件数装载率
     */
    @TableField("quantity_load_ratio")
    private BigDecimal quantityLoadRatio;

    /**
     * 装载率计算JSON(分子，分母)
     */
    @TableField("load_ratio_calculate_json")
    private String loadRatioCalculateJson;

    /**
     * 承运商品类型，0：标品，1：鲜果
     */
    @TableField("carry_type")
    private Integer carryType;

    /**
     * 蚁群智能排线距离
     */
    @TableField("ant_distance")
    private BigDecimal antDistance;


}
