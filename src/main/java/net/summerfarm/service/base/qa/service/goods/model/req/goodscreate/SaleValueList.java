package net.summerfarm.service.base.qa.service.goods.model.req.goodscreate;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SaleValueList {
    private Date createTime;
    private String creator;
    private List<String> formatStr;
    private Integer formatType;
    private Integer id;
    private String name;
    private Integer status;
    private Integer type;
    private Boolean show;
    private String productsPropertyValue;
    private String weight;
    private String weightType;
    private String firstUnit;
    private Integer firstWeight;
    private Integer secondWeight;
    private String secondUnit;
    private Integer productsPropertyId;
}