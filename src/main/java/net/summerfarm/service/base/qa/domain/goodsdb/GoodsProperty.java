package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 属性表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_property")
public class GoodsProperty {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 属性名
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 性质，0=通用，1=自定义
     */
    @TableField(value = "nature")
    private Integer nature;

    /**
     * 类型：1=SPU属性，2=SKU属性，3=商品参数，4=货品参数，5=特殊属性，6=包装单位
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 是否必填，0=否，1=是
     */
    @TableField(value = "required_flag")
    private Integer requiredFlag;

    /**
     * 格式类型：1=单选，2=文本,3=系统预设
     */
    @TableField(value = "format_rule")
    private Integer formatRule;

    /**
     * 属性值列表
     */
    @TableField(value = "format_value")
    private String formatValue;

    /**
     * 状态：0、失效 1、有效
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}