package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售商品实体类
 * <AUTHOR>
 * @date 2022/5/12 17:57:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("market_item")
public class MarketItem implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键Id
     */
    private Long marketId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格备注
     */
    private String weightNotes;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 品牌名称
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 供应商Id
     */
    private String supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * @see com.cosfo.manage.common.context.MarketDeleteFlagEnum
     */
    private Integer deleteFlag;

    /**
     * 货源类型
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    private static final long serialVersionUID = 1L;
}
