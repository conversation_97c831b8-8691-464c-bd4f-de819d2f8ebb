package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 出库任务波次配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-341 11:28:59
 */
@Getter
@Setter
@TableName("wms_stock_task_wave_config")
public class WmsStockTaskWaveConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 城配仓编号
     */
    @TableField("store_no")
    private Integer storeNo;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 波次时间
     */
    @TableField("wave_time")
    private String waveTime;

    /**
     * 波次状态（0：禁用，1：启用）
     */
    @TableField("wave_status")
    private Integer waveStatus;

    /**
     * 创建人
     */
    @TableField("create_operator")
    private String createOperator;

    /**
     * 更新人
     */
    @TableField("update_operator")
    private String updateOperator;

    /**
     * 波次sku限制: 1.非代销不入仓品 2.代销不入仓品 3.两种都有
     */
    @TableField("wave_sku_option")
    private Integer waveSkuOption;


}
