package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 代仓货品关联表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_agent_mapping")
public class GoodsAgentMapping {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * sku编码
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * spu编码
     */
    @TableField(value = "spu")
    private String spu;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 代理供应商租户Id
     */
    @TableField(value = "agent_tenant_id")
    private Long agentTenantId;

    /**
     * skuId
     */
    @TableField(value = "sku_id")
    private Long skuId;

    /**
     * 代理skuId
     */
    @TableField(value = "agent_sku_id")
    private Long agentSkuId;

    /**
     * spuId
     */
    @TableField(value = "spu_id")
    private Long spuId;

    /**
     * 代理spuId
     */
    @TableField(value = "agent_spu_id")
    private Long agentSpuId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}