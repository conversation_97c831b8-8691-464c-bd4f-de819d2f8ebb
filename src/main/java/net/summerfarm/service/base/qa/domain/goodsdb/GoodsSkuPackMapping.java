package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 货品SKU包装关联表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_sku_pack_mapping")
public class GoodsSkuPackMapping {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * sku编码
     */
    @TableField(value = "sku")
    private String sku;

    /**
     * 包装名称
     */
    @TableField(value = "pack_name")
    private String packName;

    /**
     * 单位比例
     */
    @TableField(value = "unit_ratio")
    private BigDecimal unitRatio;

    /**
     * 基础标志 0=否，1=是
     */
    @TableField(value = "base_flag")
    private Integer baseFlag;

    /**
     * 规格
     */
    @TableField(value = "specification")
    private String specification;

    /**
     * 规格单位
     */
    @TableField(value = "specification_unit")
    private String specificationUnit;

    /**
     * 规格类型，0-容量*数量，1-区间
     */
    @TableField(value = "specification_type")
    private Integer specificationType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}