package net.summerfarm.service.base.qa.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.DTO.ATPDto;
import net.summerfarm.service.base.qa.DTO.ATPRequestDto;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.rpc.service.GenericService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 类<code>Doc</code>用于：TODO
 * dubbo泛化调用服务
 * <AUTHOR>
 * @ClassName DubboInvokeController
 * @date 2025-02-07
 */
@Slf4j
@RestController
@RequestMapping(value = "/dubboInvoke")
public class DubboInvokeController {


    /**
     * 泛化调用接口
     * @param param
     * @return
     */
    @PostMapping(value = "/referenceInvoke")
    public ATPDto reference(@RequestBody String param){
        ATPRequestDto atpRequestDto = JSONObject.parseObject(param, ATPRequestDto.class);
        System.out.println(atpRequestDto);
        // 默认为dev环境
        String env = "fac8164c-1da8-43d2-bf49-e187bda7fcb4";
        String tag = "dev";
        if (atpRequestDto.getEnv().equals("qa")){
            env = "34792f7a-aaa2-41ee-8a7f-53be483c2533";
            tag = "qa";
        }
        try {
            ApplicationConfig application = new ApplicationConfig();
            application.setName("quantity-service");

            RegistryConfig registry = new RegistryConfig();
            String serverAddr = "test-nacos.summerfarm.net:11000";
            registry.setAddress(serverAddr);
            registry.setProtocol("nacos");
            registry.setParameters(Collections.singletonMap("namespace", env));

            ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
            application.setRegistry(registry);
            reference.setApplication(application);
            reference.setInterface(atpRequestDto.getInterfaceName());
            reference.setGeneric(true);
            reference.setVersion("1.0.0");
            reference.setGroup("online");
            reference.setTag(tag);

            log.info("reference:{}",JSONObject.toJSONString(reference));
            GenericService genericService = reference.get();
            Object result = genericService.$invoke(atpRequestDto.getMethodName(), atpRequestDto.getParamTypes(), atpRequestDto.getParams());
            log.info("dubbo调用结果：{}",JSONObject.toJSONString(result));
            return ATPDto.builder()
                    .dubboResponse(result)
                    .message("success")
                    .build();
        }catch (Exception e){
            log.info("dubbo调用失败：{}",e);
            return ATPDto.builder()
                    .dubboResponse(e)
                    .message("fail")
                    .build();
        }
    }


}
