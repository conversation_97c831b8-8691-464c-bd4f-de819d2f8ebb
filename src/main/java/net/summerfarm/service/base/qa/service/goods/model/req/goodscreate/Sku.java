/**
  * Copyright 2024 bejson.com 
  */
package net.summerfarm.service.base.qa.service.goods.model.req.goodscreate;
import lombok.Data;

import java.util.List;

@Data
public class Sku {
    private String sku;
    private String weight;
    private Integer type;
    private Integer subType;
    private List<String> SubTypeArray;
    private String adminId;
    private Integer extType;
    private Integer averagePriceFlag;
    private String unit;
    private String origin;
    private String maturity;
    private Integer afterSaleQuantity;
    private Integer costPrice;
    private Integer marketPrice;
    private Boolean show;
    private Integer limitedQuantity;
    private String info;
    private Integer baseSaleUnit;
    private Integer baseSaleQuantity;
    private String skuPic;
    private String createStatus;
    private String commentPic;
    private String commentInfo;
    private String commentStore;
    private String commentPrice;
    private Integer createType;
    private String afterSaleUnit;
    private Integer samplePool;
    private Integer isDomestic;
    private Boolean role;
    private String skuName;
    private Double weightNum;
    private String volume;
    private String msg;
    private String auditFlag;
    private String createRemark;
    private Boolean isNew;
    private List<String> areaSkuVOS;
    private List<SaleValueData> saleValueData;
    private List<ProductLabelValueVos> productLabelValueVos;
    private List<SaleValueList> saleValueList;
    private Double netWeightNum;
    private String netWeightUnit;
    private String buyerName;
    private Integer buyerId;
}