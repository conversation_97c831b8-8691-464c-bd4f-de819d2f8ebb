package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.WarehouseInventoryMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * sku配送中心仓库映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-234 14:55:28
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface WarehouseInventoryMappingMapper extends BaseMapper<WarehouseInventoryMapping> {

}
