package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 城市实际库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-144 15:53:11
 */
@Getter
@Setter
@TableName("area_store")
public class AreaStore implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 城市编号
     */
    @TableField("area_no")
    private Integer areaNo;

    /**
     * sku编号
     */
    @TableField("sku")
    private String sku;

    /**
     * 仓库库存数量
     */
    @TableField("quantity")
    private Integer quantity;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 采购负责人ID
     */
    @TableField("admin_id")
    private Integer adminId;

    /**
     * 采购提前期
     */
    @TableField("lead_time")
    private Integer leadTime;

    /**
     * 市场价
     */
    @TableField("market_price")
    private BigDecimal marketPrice;

    /**
     * 采购价
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    @TableField("price_status")
    private Integer priceStatus;

    /**
     * 线上库存
     */
    @TableField("online_quantity")
    private Integer onlineQuantity;

    /**
     * 销售冻结
     */
    @TableField("sale_lock_quantity")
    private Integer saleLockQuantity;

    /**
     * 锁定库存
     */
    @TableField("lock_quantity")
    private Integer lockQuantity;

    /**
     * 在途库存
     */
    @TableField("road_quantity")
    private Integer roadQuantity;

    /**
     * 是否同步
     */
    @TableField("sync")
    private Integer sync;

    /**
     * 安全库存
     */
    @TableField("safe_quantity")
    private Integer safeQuantity;

    /**
     * 变化值
     */
    @TableField("`change`")
    private Integer change;

    /**
     * sku状态,0正常,1采购入库中,2出入库中,3盘点中
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 是否自动生成转换任务
     */
    @TableField("auto_transfer")
    private Integer autoTransfer;

    /**
     * 是否支持 预留库存
     */
    @TableField("support_reserved")
    private Integer supportReserved;

    /**
     * 预留库存最大值
     */
    @TableField("reserve_max_quantity")
    private Integer reserveMaxQuantity;

    /**
     * 预留库存最小值
     */
    @TableField("reserve_min_quantity")
    private Integer reserveMinQuantity;

    /**
     * 预留库存使用值
     */
    @TableField("reserve_use_quantity")
    private Integer reserveUseQuantity;

    /**
     * 预警库存
     */
    @TableField("warning_quantity")
    private Integer warningQuantity;

    /**
     * 0 未提醒预警 1 已提醒预警
     */
    @TableField("send_warning_flag")
    private Integer sendWarningFlag;

    /**
     * 采购预售库存
     */
    @TableField("advance_quantity")
    private Integer advanceQuantity;

    /**
     * 租户id(saas品牌方)，鲜沐为1
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库租户id(saas品牌方)，鲜沐为1
     */
    @TableField("warehouse_tenant_id")
    private Long warehouseTenantId;

    /**
     * 货主编码
     */
    @TableField("owner_code")
    private String ownerCode;

    /**
     * 货主名称
     */
    @TableField("owner_name")
    private String ownerName;


}
