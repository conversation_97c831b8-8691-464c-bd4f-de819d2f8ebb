package net.summerfarm.service.base.qa.service.order;

import net.summerfarm.service.base.qa.mapper.xianmudb.MallMapper;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;

import static org.reflections.Reflections.log;

@Service
public class OrderPayService {
    @Resource
    private  CreatXmOrder creatXmOrder;
    @Resource
    private MallMapper mapper;
    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);
    public String orderPay(String orderNo) throws Exception{
        log.info("查询对应的master_order");
            String masterNo = mapper.GetMasterNo(orderNo);
            if (masterNo.isEmpty()){
                return "订单对应的masterNo 没找到";
            }
        log.info("查询订单对应的客户信息");
        Integer m_id = mapper.GetMid(masterNo);
        log.info("更新客户的余额和role_id");
        Integer update = mapper.UpdateRechargeAmount(m_id);
        log.info("查询客户的手机号");
        String phone = mapper.GetPhone(m_id);
        String url = "https://devh5.summerfarm.net//openid";
        Map<String, String> headers = new HashMap<>();
        String tokens =  creatXmOrder.getOpenToken(phone.toString());
        headers.put("Token", tokens);
        Map<String, Object> payParams = new HashMap<>();
        payParams.put("orderNo",masterNo);
        payParams.put("type",1);
        payParams.put("payurl","//devh5.summerfarm.net/home.html");
        String resultPay=null;
        if (mapper.GetOrderType(orderNo) == 30)
        {
            resultPay = okHttpUtils.sendCustomJsonPost("https://devshunluda.cosfo.cn" + "/payment/pay?masterOrderNo="+masterNo+"&payChannel=1",payParams.toString(), headers);
        }
        else {
            resultPay = okHttpUtils.sendCustomJsonPost("https://devh5.summerfarm.net" + "/payment/pay?masterOrderNo="+masterNo+"&payChannel=1",payParams.toString(), headers);
        }
        log.info(resultPay);
        return resultPay;
    }
}
