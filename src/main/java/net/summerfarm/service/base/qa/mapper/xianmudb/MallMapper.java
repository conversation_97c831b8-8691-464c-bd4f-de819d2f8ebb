package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
@DS("XIANMU_DB")
public interface MallMapper {
    String GetMasterNo(@Param("orderNo") String orderNo);
    Integer GetMid(@Param("masterNo") String masterNo);

    Integer UpdateRechargeAmount(@Param("Mid") Integer Mid);
    String GetPhone(@Param("Mid") Integer Mid);
    Integer GetOrderType(@Param("orderNo") String orderNo);
}
