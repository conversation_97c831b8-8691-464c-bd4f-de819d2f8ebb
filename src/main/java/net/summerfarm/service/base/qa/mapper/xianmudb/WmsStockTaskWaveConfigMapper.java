package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.WmsStockTaskWaveConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 出库任务波次配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-341 11:28:59
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface WmsStockTaskWaveConfigMapper extends BaseMapper<WmsStockTaskWaveConfig> {

}
