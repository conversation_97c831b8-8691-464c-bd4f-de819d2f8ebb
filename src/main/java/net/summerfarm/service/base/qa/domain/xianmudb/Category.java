package net.summerfarm.service.base.qa.domain.xianmudb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "品类实体类")
@Data
public class Category implements Serializable {

    /**
     * 水果类型
     */
    public static final int FRUIT_TYPE = 4;

    @ApiModelProperty(value = "品类id")
    private Integer id;

    @ApiModelProperty(value = "父品类id")
    private Integer parentId;

    @ApiModelProperty(value = "品类名称")
    private String category;

    @ApiModelProperty(value = "状态")
    private String outdated;

    @ApiModelProperty(value = "1 全部,2乳制品,3非乳制品,4水果")
    private Integer type;
}