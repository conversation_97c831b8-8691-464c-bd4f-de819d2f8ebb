package net.summerfarm.service.base.qa.mapper.cosfodb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.cosfodb.ProductPricingSupply;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


/**
 * @discription 供价持久层
 * <AUTHOR>
 * @date 2022/5/12
 */
@DS("COFSO_DB")
@Mapper
public interface ProductPricingSupplyMapper extends BaseMapper<ProductPricingSupply> {

}
