package net.summerfarm.service.base.qa.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;

/**
 * 类<code>Doc</code>用于：TODO
 * es的索引实体类
 * <AUTHOR>
 * @ClassName AICase
 * @date 2025-04-15
 */
@Document(indexName = "ai_case")
@Data
public class AICaseES {

    @Id
    private String projectId;

    @Field
    private String projectId2;
    @Field
    private String prompt;
    @Field
    private String response;
    @Field
    private String operateTime;

    @Field
    private String fileName;

    public AICaseES() {
    }

    public AICaseES(String projectId,  String projectId2, String prompt, String response, String operateTime, String fileName) {
        this.projectId = projectId;
        this.projectId2 = projectId2;
        this.prompt = prompt;
        this.response = response;
        this.operateTime = operateTime;
        this.fileName = fileName;
    }
}
