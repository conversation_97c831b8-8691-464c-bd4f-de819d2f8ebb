package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 后台类目
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "back_category")
public class BackCategory {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 父ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 级别
     */
    @TableField(value = "`level`")
    private Integer level;


    /**
     * 性质，0=通用，1=自定义
     */
    @TableField(value = "nature")
    private Integer nature;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 0=正常，1=已删除
     */
    @TableField(value = "deleted_flag")
    private Integer deletedFlag;

    /**
     * 1=全部,2=乳制品,3=非乳制品,4=水果，5=其他(1,3历史类型，现有类型2,4,5)
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 税率分类编码
     */
    @TableField(value = "tax_rate_code")
    private String taxRateCode;

    /**
     * 税率
     */
    @TableField(value = "tax_rate_value")
    private BigDecimal taxRateValue;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}