package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.TmsDeliveryBatch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 配送批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-242 18:35:56
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface TmsDeliveryBatchMapper extends BaseMapper<TmsDeliveryBatch> {

}
