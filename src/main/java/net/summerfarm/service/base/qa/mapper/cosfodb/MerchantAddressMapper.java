package net.summerfarm.service.base.qa.mapper.cosfodb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.cosfodb.MerchantAddress;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


@Mapper
@DS("COFSO_DB")
public interface MerchantAddressMapper extends BaseMapper<MerchantAddress> {

}
