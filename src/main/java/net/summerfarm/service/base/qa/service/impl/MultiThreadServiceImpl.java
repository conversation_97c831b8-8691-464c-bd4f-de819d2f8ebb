package net.summerfarm.service.base.qa.service.impl;

import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.service.MultiThreadService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import net.summerfarm.service.base.qa.utils.ThreadPoolUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.SQLOutput;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description: 多线程执行工具类
 * @author: chenwenjie
 * @time: 2021/8/24 1:31 下午
 */

@Service
public class MultiThreadServiceImpl implements MultiThreadService {

    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);


    @Override
    public List<String> execute(Map<String, String> params) {
        String url = params.get("url");
        String param = params.get("param");
        String token = params.get("Token");
        Integer threadCount = 1;
        if (!ObjectUtils.isEmpty(params.get("threadCount"))){
            threadCount = Integer.parseInt(params.get("threadCount"));
        }
        return doExecute(url, param, token, threadCount);
    }

    //执行多线程运行
    private List<String> doExecute(String url, String param, String token, Integer threadCount){
        ThreadPoolExecutor executor = ThreadPoolUtil.getExecutor(threadCount);
        CountDownLatch countDownLatch = new CountDownLatch(threadCount);
        List<String> results = new ArrayList<>();
        Long begin = System.currentTimeMillis();
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    results.add(okHttpUtils.sendJsonPost(url, param, token, null, null));
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
            executor.shutdown();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Long end = System.currentTimeMillis();
        System.out.println(begin - end );
        return results ;
    }
}