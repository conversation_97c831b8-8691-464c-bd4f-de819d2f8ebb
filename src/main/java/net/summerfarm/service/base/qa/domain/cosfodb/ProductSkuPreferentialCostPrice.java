package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 省心定报价
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@TableName("product_sku_preferential_cost_price")
public class ProductSkuPreferentialCostPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 可用数量
     */
    private Integer availableQuantity;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记 0未删除，1删除
     */
    private Integer deleted;


}
