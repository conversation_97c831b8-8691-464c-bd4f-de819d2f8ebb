/**
  * Copyright 2024 bejson.com 
  */
package net.summerfarm.service.base.qa.service.goods.model.req.goodscreate;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class KeyValueList {

    private Date createTime;
    private String creator;
    private Integer formatType;
    private Integer id;
    private String name;
    private Integer status;
    private Integer type;
    private List<String> formatStr;
    private String value;
    private Integer productsPropertyId;
    private String productsPropertyValue;

}