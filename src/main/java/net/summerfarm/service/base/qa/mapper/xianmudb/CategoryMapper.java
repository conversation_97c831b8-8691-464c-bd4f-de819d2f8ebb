package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.xianmudb.Category;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@DS("XIANMU_DB")
@Mapper
@Repository
public interface CategoryMapper extends BaseMapper<Category> {

}
