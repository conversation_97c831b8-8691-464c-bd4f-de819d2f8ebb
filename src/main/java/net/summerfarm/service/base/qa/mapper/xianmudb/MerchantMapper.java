package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.Merchant;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 商户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-148 10:53:04
 */
@DS("XIANMU_DB")
@Mapper
@Repository
public interface MerchantMapper extends BaseMapper<Merchant> {

}
