package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.Purchases;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 采购单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-219 18:39:26
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface PurchasesMapper extends BaseMapper<Purchases> {

}
