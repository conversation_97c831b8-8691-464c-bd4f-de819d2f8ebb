package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.WarehouseStorageCenter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 仓储中心 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-220 17:29:26
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface WarehouseStorageCenterMapper extends BaseMapper<WarehouseStorageCenter> {

}
