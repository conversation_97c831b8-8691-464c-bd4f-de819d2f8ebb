package net.summerfarm.service.base.qa.service.goods;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.service.base.qa.config.ConfigValue;
import net.summerfarm.service.base.qa.domain.xianmudb.Inventory;
import net.summerfarm.service.base.qa.mapper.xianmudb.AreaMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.AreaStoreMapper;
import net.summerfarm.service.base.qa.mapper.xianmudb.InventoryMapper;
import net.summerfarm.service.base.qa.model.Area;
import net.summerfarm.service.base.qa.model.AreaStore;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.service.goods.node.GoodsNode;
import net.summerfarm.service.base.qa.service.supply.SupplyService;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CreateNewGoods {
    @Resource
    private GoodsNode goodsNode;

    @Resource
    private AreaMapper areaMapper;
    @Resource
    private TokenService tokenService;
    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    @Resource
    private  InventoryMapper inventoryMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private SupplyService supplyService;

    @Resource
    private ConfigValue configValue;
    public String NewGoods(String goodsName,String skuType,String area) throws Exception  {

        if(StringUtils.isEmpty(goodsName) ){
            goodsName ="自动创建商品";
        }
        if(StringUtils.isEmpty(skuType) ){
            skuType ="1";
        }
        int type=0;
        if (skuType.equals("1") ||skuType.equals("3")){
            type=0;
        }if (skuType.equals("5")){
            type=2;
        }
        Map<String, String> headers = new HashMap<>();
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        headers.put("Token", token);
        String defaultBody = "{\n" +
                "    \"pdName\": \""+goodsName+"\",\n" +
                "    \"pddetail\": \""+goodsName+"副标题\",\n" +
                "    \"otherSlogan\": \"图片中是很好看的商品图\",\n" +
                "    \"afterSaleTime\": 48,\n" +
                "    \"warnTime\": 309,\n" +
                "    \"qualityTime\": 199,\n" +
                "    \"qualityTimeUnit\": \"day\",\n" +
                "    \"qualityTimeType\": 0,\n" +
                "    \"refundType\": \"拍多/拍错/不想要;缺货;其他\",\n" +
                "    \"realName\": \""+goodsName+"实物名称\",\n" +
                "    \"afterSaleType\": \"商品数量不符;包装问题;商品品质问题;保质期不符;平台发错货;其他\",\n" +
                "    \"storageLocation\": 1,\n" +
                "    \"afterSaleUnit\": \"\",\n" +
                "    \"auditStatus\": 1,\n" +
                "    \"createStatus\": 1,\n" +
                "    \"createType\": \"\",\n" +
                "    \"commentPic\": [],\n" +
                "    \"productIntroduction\": \"\",\n" +
                "    \"picturePath\": \"test/tpoxrx2lhpc1gfad2.jpg\",\n" +
                "    \"detailPicture\": \"test/kj6oafqpeps1gfdnq.jpg,test/o9xq3j09okd1gfgur.jpg,test/e83k5q2757h1gft6m.jpg\",\n" +
                "    \"categoryId\": 2160,\n" +
                "    \"keyValueList\": [\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatType\": 3,\n" +
                "            \"id\": 1,\n" +
                "            \"name\": \"产地\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"formatStr\": [],\n" +
                "            \"value\": \"浙江\",\n" +
                "            \"productsPropertyId\": 1,\n" +
                "            \"productsPropertyValue\": \"浙江\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatType\": 3,\n" +
                "            \"id\": 2,\n" +
                "            \"name\": \"品牌\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"formatStr\": [],\n" +
                "            \"value\": \"深海鱼\",\n" +
                "            \"productsPropertyId\": 2,\n" +
                "            \"productsPropertyValue\": \"深海鱼\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatStr\": [\n" +
                "                \"冷藏\",\n" +
                "                \"冷冻\",\n" +
                "                \"常温\"\n" +
                "            ],\n" +
                "            \"formatType\": 2,\n" +
                "            \"id\": 4,\n" +
                "            \"name\": \"储藏区域\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"value\": \"冷藏\",\n" +
                "            \"productsPropertyId\": 4,\n" +
                "            \"productsPropertyValue\": \"冷藏\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatType\": 3,\n" +
                "            \"id\": 5,\n" +
                "            \"name\": \"储藏温度\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"formatStr\": [],\n" +
                "            \"value\": \"30度\",\n" +
                "            \"productsPropertyId\": 5,\n" +
                "            \"productsPropertyValue\": \"30度\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatType\": 3,\n" +
                "            \"id\": 17,\n" +
                "            \"name\": \"使用方法\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"formatStr\": [],\n" +
                "            \"value\": \"高温烹炸\",\n" +
                "            \"productsPropertyId\": 17,\n" +
                "            \"productsPropertyValue\": \"高温烹炸\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "            \"creator\": \"初始化\",\n" +
                "            \"formatType\": 3,\n" +
                "            \"id\": 18,\n" +
                "            \"name\": \"成分\",\n" +
                "            \"status\": 1,\n" +
                "            \"type\": 0,\n" +
                "            \"formatStr\": [],\n" +
                "            \"value\": \"鱼肉\",\n" +
                "            \"productsPropertyId\": 18,\n" +
                "            \"productsPropertyValue\": \"鱼肉\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"salePropertyList\": [\n" +
                "        {\n" +
                "            \"id\": 7,\n" +
                "            \"name\": \"规格\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 21,\n" +
                "            \"name\": \"口味\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 27,\n" +
                "            \"name\": \"尺寸\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"createRemark\": \"{\\\"commentPic\\\":[]}\",\n" +
                "    \"skuList\": [\n" +
                "        {\n" +
                "            \"sku\": \"新增SKU\",\n" +
                "            \"weight\": \"(深海鱼油销售说明)\",\n" +
                "            \"type\": "+type+",\n" +
                "            \"subType\": "+skuType+",\n" +
                "            \"SubTypeArray\": [\n" +
                "                \"0\",\n" +
                "                \"3\"\n" +
                "            ],\n" +
                "            \"adminId\": \"\",\n" +
                "            \"extType\": 0,\n" +
                "            \"averagePriceFlag\": 0,\n" +
                "            \"unit\": \"盒\",\n" +
                "            \"origin\": \"\",\n" +
                "            \"maturity\": \"\",\n" +
                "            \"afterSaleQuantity\": 15,\n" +
                "            \"costPrice\": 0,\n" +
                "            \"marketPrice\": 0,\n" +
                "            \"show\": true,\n" +
                "            \"limitedQuantity\": 0,\n" +
                "            \"areaSkuVOS\": [],\n" +
                "            \"info\": \"\",\n" +
                "            \"baseSaleUnit\": 1,\n" +
                "            \"baseSaleQuantity\": 1,\n" +
                "            \"skuPic\": \"\",\n" +
                "            \"saleValueList\": [\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-01 17:06:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatStr\": [\n" +
                "                        \"mL\",\n" +
                "                        \"L\",\n" +
                "                        \"G\",\n" +
                "                        \"KG\",\n" +
                "                        \"个\",\n" +
                "                        \"斤\",\n" +
                "                        \"箱\",\n" +
                "                        \"盒\",\n" +
                "                        \"包\",\n" +
                "                        \"袋\",\n" +
                "                        \"瓶\",\n" +
                "                        \"罐\",\n" +
                "                        \"桶\",\n" +
                "                        \"卷\",\n" +
                "                        \"块\",\n" +
                "                        \"片\",\n" +
                "                        \"颗\",\n" +
                "                        \"支\",\n" +
                "                        \"条\",\n" +
                "                        \"只\",\n" +
                "                        \"张\",\n" +
                "                        \"套\",\n" +
                "                        \"组\"\n" +
                "                    ],\n" +
                "                    \"formatType\": 0,\n" +
                "                    \"id\": 7,\n" +
                "                    \"name\": \"规格\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"productsPropertyValue\": \"1KG*5KG\",\n" +
                "                    \"weight\": \"容量*数量\",\n" +
                "                    \"weightType\": \"净重\",\n" +
                "                    \"firstUnit\": \"KG\",\n" +
                "                    \"firstWeight\": 1,\n" +
                "                    \"secondWeight\": 5,\n" +
                "                    \"secondUnit\": \"KG\",\n" +
                "                    \"productsPropertyId\": 7\n" +
                "                },\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatType\": 3,\n" +
                "                    \"id\": 21,\n" +
                "                    \"name\": \"口味\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"formatStr\": [],\n" +
                "                    \"value\": \"爽口\",\n" +
                "                    \"productsPropertyId\": 21,\n" +
                "                    \"productsPropertyValue\": \"爽口\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-04 17:06:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatType\": 3,\n" +
                "                    \"id\": 27,\n" +
                "                    \"name\": \"尺寸\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"formatStr\": [],\n" +
                "                    \"value\": \"2米\",\n" +
                "                    \"productsPropertyId\": 27,\n" +
                "                    \"productsPropertyValue\": \"2米\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"createStatus\": \"\",\n" +
                "            \"commentPic\": \"\",\n" +
                "            \"commentInfo\": \"\",\n" +
                "            \"commentStore\": \"\",\n" +
                "            \"commentPrice\": \"\",\n" +
                "            \"createType\": 0,\n" +
                "            \"afterSaleUnit\": \"条\",\n" +
                "            \"samplePool\": 1,\n" +
                "            \"isDomestic\": 1,\n" +
                "            \"productLabelValueVos\": [\n" +
                "                {\n" +
                "                    \"labelField\": \"replenishment_plan\",\n" +
                "                    \"labelId\": 1001,\n" +
                "                    \"labelName\": \"参与补货计划\",\n" +
                "                    \"labelValue\": 0\n" +
                "                },\n" +
                "                {\n" +
                "                    \"labelField\": \"supplier_visible\",\n" +
                "                    \"labelId\": 1002,\n" +
                "                    \"labelName\": \"供应商可见\",\n" +
                "                    \"labelValue\": 0\n" +
                "                },\n" +
                "                {\n" +
                "                    \"labelField\": \"consignment_sku\",\n" +
                "                    \"labelId\": 1003,\n" +
                "                    \"labelName\": \"SKU代售\",\n" +
                "                    \"labelValue\": 0\n" +
                "                }\n" +
                "            ],\n" +
                "            \"role\": true,\n" +
                "            \"skuName\": \""+goodsName+"\",\n" +
                "            \"weightNum\": 3.5,\n" +

                "            \"saleValueData\": [\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-01 17:06:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatStr\": [\n" +
                "                        \"mL\",\n" +
                "                        \"L\",\n" +
                "                        \"G\",\n" +
                "                        \"KG\",\n" +
                "                        \"个\",\n" +
                "                        \"斤\",\n" +
                "                        \"箱\",\n" +
                "                        \"盒\",\n" +
                "                        \"包\",\n" +
                "                        \"袋\",\n" +
                "                        \"瓶\",\n" +
                "                        \"罐\",\n" +
                "                        \"桶\",\n" +
                "                        \"卷\",\n" +
                "                        \"块\",\n" +
                "                        \"片\",\n" +
                "                        \"颗\",\n" +
                "                        \"支\",\n" +
                "                        \"条\",\n" +
                "                        \"只\",\n" +
                "                        \"张\",\n" +
                "                        \"套\",\n" +
                "                        \"组\"\n" +
                "                    ],\n" +
                "                    \"formatType\": 0,\n" +
                "                    \"id\": 7,\n" +
                "                    \"name\": \"规格\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"productsPropertyValue\": \"1KG*5KG\",\n" +
                "                    \"weight\": \"容量*数量\",\n" +
                "                    \"weightType\": \"净重\",\n" +
                "                    \"firstUnit\": \"KG\",\n" +
                "                    \"firstWeight\": 1,\n" +
                "                    \"secondWeight\": 5,\n" +
                "                    \"secondUnit\": \"KG\",\n" +
                "                    \"productsPropertyId\": 7\n" +
                "                },\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-11 17:07:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatType\": 3,\n" +
                "                    \"id\": 21,\n" +
                "                    \"name\": \"口味\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"formatStr\": [],\n" +
                "                    \"value\": \"爽口\",\n" +
                "                    \"productsPropertyId\": 21,\n" +
                "                    \"productsPropertyValue\": \"爽口\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"createTime\": \"2020-05-04 17:06:21\",\n" +
                "                    \"creator\": \"初始化\",\n" +
                "                    \"formatType\": 3,\n" +
                "                    \"id\": 27,\n" +
                "                    \"name\": \"尺寸\",\n" +
                "                    \"status\": 1,\n" +
                "                    \"type\": 1,\n" +
                "                    \"show\": true,\n" +
                "                    \"formatStr\": [],\n" +
                "                    \"value\": \"2米\",\n" +
                "                    \"productsPropertyId\": 27,\n" +
                "                    \"productsPropertyValue\": \"2米\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"volume\": \"0.150*0.160*0.200\",\n" +
                "            \"msg\": \"深海鱼油销售说明\",\n" +
                "            \"auditFlag\": \"\",\n" +
                "            \"createRemark\": \"{\\\"commentPic\\\":\\\"\\\",\\\"commentInfo\\\":\\\"\\\",\\\"commentStore\\\":\\\"\\\",\\\"commentPrice\\\":\\\"\\\",\\\"createType\\\":0}\",\n" +
                "            \"isNew\": true,\n" +
                "             \"buyerName\": \"花名\",\n" +
                "             \"buyerId\": 4,\n" +
                "            \"netWeightNum\":1.88,\n" +
                "            \"netWeightUnit\":\"KG\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        System.out.println(defaultBody);
        JSONObject data = new JSONObject(goodsNode.goodsCreate(defaultBody));
        JSONObject outerApplicationMap = new JSONObject(String.valueOf(data.get("outerApplicationMap")));
        String skuId = (String) outerApplicationMap.get("applicationItemCode:null");
        LambdaQueryWrapper<Inventory> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(Inventory::getSku,skuId);
        Inventory entity = new Inventory();
        entity.setAuditStatus(1);
        entity.setOutdated(0);
        inventoryMapper.update(entity, updateWrapper);

        //填写的区域非空
        if (!area.isEmpty()){
            LambdaQueryWrapper<Area> AreaWrapper = new LambdaQueryWrapper<>();
            if (!area.matches("[1-9]\\d*")){
                AreaWrapper.like(Area::getAreaName,area);
            }
            if (area.matches("[1-9]\\d*")){
                AreaWrapper.eq(Area::getAreaNo,area);
            }
            List<Area> areaDbs = areaMapper.selectList(AreaWrapper);
            if (areaDbs.isEmpty()){
                return skuId;
            }
            Integer parentNo =areaDbs.get(0).getParentNo();
            Integer areaNo =areaDbs.get(0).getAreaNo();

            //根据运营区域寻找对应的库存仓
            String logisticsResult = okHttpUtils.sendGet(configValue.summerfarmManageUri + "/warehouse/logistics/usable-storage?areaNo="+areaNo+"&sku="+skuId+"", null, headers);
            JSONObject resultLogistics = new JSONObject(logisticsResult);
            JSONArray dataLogistics = new JSONArray(resultLogistics.get("data").toString());
            if (dataLogistics.length()==0){
                return "500";
            }
            JSONObject Logistics = new JSONObject(dataLogistics.get(0).toString());
            String warehouseno = Logistics.getString("warehouseNo");

            String data1 = " {\n" +
                    "        \"warehouseNo\": \""+warehouseno+"\",\n" +
                    "        \"parentNo\": \""+parentNo+"\",\n" +
                    "        \"areaNo\": \""+areaNo+"\",\n" +
                    "        \"openSale\": 3,\n" +
                    "        \"price\": 1,\n" +
                    "        \"mType\": 0,\n" +
                    "        \"cornerStatus\": 1,\n" +
                    "        \"salesMode\": 0,\n" +
                    "        \"ladderPrice\": \"[]\",\n" +
                    "        \"sku\": \""+skuId+"\",\n" +
                    "        \"seconds\": 0,\n" +
                    "        \"info\": \"\",\n" +
                    "        \"_index\": 0,\n" +
                    "        \"_rowKey\": \""+areaNo+"\",\n" +
                    "        \"show\": true\n" +
                    "    }";
            JSONObject areaSku = new JSONObject(data1);
            areaSku.put("warehouseNo", warehouseno);//473是自动下单的仓库
            areaSku.put("sku", skuId);
            areaSku.put("_rowKey", areaNo);
            areaSku.put("areaNo", areaNo);
            areaSku.put("parentNo", parentNo);
            ArrayList areaSkus = new ArrayList<>();
            areaSkus.add(areaSku);
            Map<String, String> params = new HashMap<>();
            /**
             商品上架
             */
            String result = okHttpUtils.sendCustomJsonPut(configValue.summerfarmManageUri + "/area-sku/batch", areaSkus.toString(), headers);

            // 通过采购入库方式增加库存，普通商品
            if (skuType.equals("3")){
                supplyService.purchaseCreate(skuId,warehouseno,100);
            }
            //POP和全品类的直接虚拟库存
            if(skuType.equals("1") ||skuType.equals("5")) {
                LambdaQueryWrapper<AreaStore> updateAreaStore = new LambdaQueryWrapper<>();
                updateAreaStore.eq(AreaStore::getAreaNo,warehouseno).eq(AreaStore::getSku,skuId);
                AreaStore entityAreaStore = new AreaStore();
                entityAreaStore.setOnlineQuantity(999);
                areaStoreMapper.update(entityAreaStore, updateAreaStore);

            }

            //寻找匹配的区域名称
        }


        return skuId;
    }
}
