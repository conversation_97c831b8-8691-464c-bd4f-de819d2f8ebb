package net.summerfarm.service.base.qa.mapper.goodsdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.domain.goodsdb.BackCategory;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

@DS("GOODS_DB")
@Mapper
@Repository
public interface BackCategoryMapper extends BaseMapper<BackCategory> {

}
