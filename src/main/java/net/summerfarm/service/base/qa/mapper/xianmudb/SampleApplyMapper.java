package net.summerfarm.service.base.qa.mapper.xianmudb;

import com.baomidou.dynamic.datasource.annotation.DS;
import net.summerfarm.service.base.qa.model.SampleApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 样品申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-144 18:09:01
 */
@Mapper
@Repository
@DS("XIANMU_DB")
public interface SampleApplyMapper extends BaseMapper<SampleApply> {

}
