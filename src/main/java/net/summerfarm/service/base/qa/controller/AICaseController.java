package net.summerfarm.service.base.qa.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.service.es.AICaseAttchmentService;
import net.summerfarm.service.base.qa.service.es.AICaseService;
import net.summerfarm.service.base.qa.service.es.ESServiceImpl;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 类<code>AICaseController</code>用于：处理前端发送的提示问题并返回一个包含message、data以及code的对象
 * <AUTHOR>
 * @ClassName AICaseController
 * @date 2025-03-13
 */
@Slf4j
@RestController
@RequestMapping(value = "/aiCase")
@CrossOrigin
public class AICaseController {

    @NacosValue(value = "${case.model}", autoRefreshed = true)
    private String caseModel;

    @NacosValue(value = "${case.smoke.model}", autoRefreshed = true)
    private String caseSmokeModel;

    @NacosValue(value = "${case.smoketest.prompt}", autoRefreshed = true)
    private String smokePrompt;

    @NacosValue(value = "${case.testpoint.prompt}", autoRefreshed = true)
    private String testPointPrompt;

    @NacosValue(value = "${case.generation.prompt}", autoRefreshed = true)
    private String testGenerationPrompt;

    @NacosValue(value = "${wiki.token}", autoRefreshed = true)
    private String wikiToken;


    @Resource
    private ESServiceImpl esService;

    @Resource
    private AICaseService aiCaseService;

    @Resource
    private AICaseAttchmentService aiCaseAttchmentService;

    @PostMapping(value = "/generate")
    @Async
    public CompletableFuture<JSONObject> ask(@RequestBody String content) {
        log.info("Received prompt: {}", content);
        JSONObject object = JSON.parseObject(content).getJSONObject("body");
        JSONObject response = new JSONObject();
        try {
            // 获取projectID
            String projectID = object.getString("projectId");
            if (projectID != null) {
                // 数据库记录
                String fileName = object.getString("fileName");
                String operateTime = object.getString("operateTime");
                aiCaseService.insertAICaseRecord(projectID, fileName, operateTime);
                // 将projectID作为key插入ES
                esService.insertToES(projectID,  projectID, object.getString("prompt"), object.getString("response"), object.getString("operateTime"), object.getString("fileName"));
            }
            response.put("code", 200);
            response.put("message", "success");
            response.put("projectId", projectID);
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error processing prompt:");
            response.put("message", "Error processing prompt");
            response.put("data", null);
            response.put("code", 500);
            return CompletableFuture.completedFuture(response);
        }
    }

    @PostMapping(value = "/editRecord")
    public JSONObject editRecord(@RequestBody String content) {
        log.info("Received prompt: {}", content);
        JSONObject object = JSON.parseObject(content).getJSONObject("body");
        JSONObject response = new JSONObject();
        try {
            String projectID = object.getString("projectId");
            // 生成随机ID
            long timestamp = System.currentTimeMillis() / 1000;
            String eightDigitTimestamp = String.valueOf(timestamp).substring(2);
            String subProjectID = projectID+"-"+eightDigitTimestamp;
            String data = "";
            if ("delete".equals(object.getString("operation"))){
                data = object.getString("originalData");
            }else if ("edit".equals(object.getString("operation"))){
                data = object.getString("originalData")+"\n->"+object.getString("updatedData");
            }else {
                data = object.getString("updatedData");
            }
            esService.editInsertToES(projectID, object.getString("operation"), subProjectID, data, object.getString("operateTime"));
            // 模拟处理提示问题并生成响应数据
            String message = "Success";
            response.put("message", message);
            response.put("editId", projectID);
            response.put("success", true);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error processing prompt: {}", e.getMessage());
            response.put("message", "Error processing prompt");
            response.put("data", null);
            response.put("code", 500);
        }
        return response;
    }

    @PostMapping(value = "/testPoint2Case")
    public JSONObject testPoint2Case(@RequestBody String content) {
        log.info("Received prompt: {}", content);
        JSONObject object = JSON.parseObject(content).getJSONObject("body");
        JSONObject response = new JSONObject();
        try {
            // 获取projectID
            String projectID = object.getString("projectId");
            if (projectID != null) {
                // 将projectID作为key插入ES
                esService.point2caseInsertToES(projectID, object.getString("selectedTestPoints"), object.getString("operateTime"), object.getString("generatedCase"));
            }
            response.put("success", true);
            response.put("code", 200);
            response.put("message", "success");
            response.put("projectId", projectID);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error processing prompt: {}", e.getMessage());
            response.put("message", "Error processing prompt");
            response.put("data", null);
            response.put("code", 500);
        }
        return response;
    }

    @PostMapping(value = "/caseExport")
    public JSONObject caseExport(@RequestBody String content) {
        log.info("Received prompt: {}", content);
        JSONObject object = JSON.parseObject(content).getJSONObject("body");
        JSONObject response = new JSONObject();
        try {
            // 获取projectID
            String projectID = object.getString("projectId");
            if (projectID != null) {
                esService.exportInsertToES(projectID, object.getString("generatedCase"), object.getString("selectedTestPoints"), object.getString("operateTime"));
            }
            response.put("code", 200);
            response.put("message", "success");
            response.put("projectId", projectID);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error processing prompt: {}", e.getMessage());
            response.put("message", "Error processing prompt");
            response.put("data", null);
            response.put("code", 500);
        }
        return response;
    }

    @GetMapping(value = "/aiReview")
    public JSONObject getAIReviewCases(@RequestParam(value = "page", defaultValue = "1") int page,
                                       @RequestParam(value = "size", defaultValue = "10") int size) {
        log.info("Fetching AI review cases with page={}, size={}", page, size);
        JSONObject response = new JSONObject();

        try {
            List<Map<String, Object>> reviewCases = aiCaseService.findCasesWithZeroFlag(page, size);

            response.put("code", 200);
            response.put("message", "success");
            response.put("data", reviewCases);
            response.put("page", page);
            response.put("size", size);
            response.put("total", aiCaseService.countCasesWithZeroFlag());

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching AI review cases: {}", e.getMessage());
            response.put("message", "Error fetching AI review cases");
            response.put("data", null);
            response.put("code", 500);
        }

        return response;
    }

    @GetMapping(value = "/getSmokeModel")
    public JSONObject getSmokeModel() {
        log.info("Fetching AI review cases with page");
        JSONObject response = new JSONObject();
        try {
            response.put("code", 200);
            response.put("prompt", smokePrompt);
            response.put("message", "success");
            response.put("model", caseSmokeModel);

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching AI review cases: {}", e.getMessage());
            response.put("message", "Error fetching AI review cases");
            response.put("data", null);
            response.put("code", 500);
        }

        return response;
    }

    @GetMapping(value = "/getCaseModel")
    public JSONObject getCaseModel() {
        log.info("Fetching AI review cases with page");
        JSONObject response = new JSONObject();
        try {
            response.put("code", 200);
            response.put("pointPrompt", testPointPrompt);
            response.put("message", "success");
            response.put("model", caseModel);
            response.put("generationPrompt", testGenerationPrompt);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching AI review cases: {}", e.getMessage());
            response.put("message", "Error fetching AI review cases");
            response.put("data", null);
            response.put("code", 500);
        }
        return response;
    }

    @PostMapping(value = "/getWikiContent")
    public JSONObject getWikiContent(@RequestBody String content) {
        log.info("Received wiki content request: {}", content);
        JSONObject object = JSON.parseObject(content);
        JSONObject response = new JSONObject();

        try {
            // 从请求中提取查询参数
            String query = object.getString("query");

            // 参数验证
            if (query == null || query.trim().isEmpty()) {
                response.put("code", 400);
                response.put("message", "查询参数不能为空");
                response.put("success", false);
                response.put("data", null);
                return response;
            }

            // 调用服务获取Wiki内容
            JSONObject wikiResult = aiCaseAttchmentService.getWikiContent(query, wikiToken);

            // 构建成功响应
            response.put("code", 200);
            response.put("message", "success");
            response.put("success", true);
            response.put("data", wikiResult);

            log.info("Successfully retrieved wiki content for query: {}", query);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching wiki content: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "获取Wiki内容失败: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
        }

        return response;
    }
}