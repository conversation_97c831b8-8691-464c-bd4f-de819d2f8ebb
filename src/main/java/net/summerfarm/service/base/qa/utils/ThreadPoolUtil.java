package net.summerfarm.service.base.qa.utils;


import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <PERSON>
 * @Date: 2020/6/19 4:45 PM
 */
public class ThreadPoolUtil {

    private static ThreadFactory nameThreadFactory = new ThreadFactoryBuilder().setNameFormat("goods-watchman-%d").build();

    /**
     * 需要结合业务本身 合理设置线程池参数
     */
    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(corePoolSize(), corePoolSize(), 10, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(100), nameThreadFactory,
            // 阻塞主线程
            new ThreadPoolExecutor.CallerRunsPolicy());


    public static ThreadPoolExecutor getExecutor() {

        // 线程池空闲时候回收核心线程
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    public static ThreadPoolExecutor getExecutor(Integer threadCount) {

        //传null，默认1个
        threadCount = threadCount == null ? 1 : threadCount;

        //新创建线程池
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(threadCount, threadCount, 10, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue <>(100), nameThreadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());

        // 线程池空闲时候回收核心线程
        threadPoolExecutor.allowCoreThreadTimeOut(true);

        return threadPoolExecutor;
    }

    private static int corePoolSize() {
        // 最多100个，
        return  100;
    }
}
