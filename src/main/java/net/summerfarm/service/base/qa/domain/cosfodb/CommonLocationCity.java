package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * common_location_city
 * <AUTHOR>
@Data
@TableName("common_location_city")
public class CommonLocationCity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 省份主键Id
     */
    private Long provinceId;

    /**
     * 城市名称
     */
    private String name;

    private static final long serialVersionUID = 1L;
}
