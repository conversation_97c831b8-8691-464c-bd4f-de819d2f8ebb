package net.summerfarm.service.base.qa.controller;

import net.summerfarm.service.base.qa.mapper.xianmudb.ProductsMapper;
import net.summerfarm.service.base.qa.service.MultiThreadService;
import net.summerfarm.service.base.qa.service.TokenService;
import net.summerfarm.service.base.qa.service.goods.node.GoodsNode;
import net.summerfarm.service.base.qa.utils.OKHttpUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: chenwenjie
 * @time: 2021/8/24 1:15 下午
 */

@RestController
@RequestMapping(value = "/thread")
public class multiThreadCroller {

    @Resource
    MultiThreadService multiThreadService ;

    @Resource
    TokenService tokenService;

    @Resource
    GoodsNode goodsNode;

    @Resource
    ProductsMapper productsMapper;

    private OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);

    @GetMapping(value = "/multi")
    public List<String> multiThread(@RequestParam("threadCount") String threadCount){
        String token = tokenService.getToken(1, "<EMAIL>", "hello1234");
        String aa = "{\"configId\":\"\",\"province\":\"河南\",\"city\":\"洛阳市\",\"areaList\":[{\"adCode\":\"410324\",\"city\":\"洛阳市\",\"area\":\"栾川县\"}],\"timeList\":[{\"beginTime\":\"01:00\",\"endTime\":\"02:00\"}]}";
        String url = "https://dev2admin.summerfarm.net/summerfarm-wnc/precise-delivery-config/upsert/save";
        Map<String, String> params = new HashMap<>();
        params.put("url", url);
        params.put("param", aa);
        params.put("threadCount", threadCount);
        params.put("Token", token);
        return multiThreadService.execute(params);
    }

    @GetMapping(value = "/search")
    public String search(@RequestParam("type") Integer type,
                           @RequestParam("userName") String userName,
                           @RequestParam("password") String password){

        return tokenService.getToken(type, userName, password);
    }

    @GetMapping(value = "/goodsCreate")
    public String goodsCreate(){

        return goodsNode.goodsCreate(null);
    }

}