package net.summerfarm.service.base.qa.mapper.repeater;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.service.base.qa.model.InterfaceLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 接口日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Mapper
@Repository
@DS("REPEATER")
public interface InterfaceLogMapper extends BaseMapper<InterfaceLog> {

}
