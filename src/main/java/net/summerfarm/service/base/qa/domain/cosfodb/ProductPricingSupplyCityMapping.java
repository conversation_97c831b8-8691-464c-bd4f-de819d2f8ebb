package net.summerfarm.service.base.qa.domain.cosfodb;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * product_pricing_supply_city_mapping
 * <AUTHOR>
@Data
@TableName("product_pricing_supply_city_mapping")
public class ProductPricingSupplyCityMapping implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 报价单主键Id
     */
    private Long productPricingSupplyId;

    /**
     * 城市Id
     */
    private Long cityId;

    /**
     * 报价方式 0、指定价  1,随鲜沐商城价
     */
    private Integer type;

    /**
     * 0、成本供价 1、报价单供价
     */
    private Integer supplyType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记 0未删除，1删除
     */
    private Integer deleted;

    private static final long serialVersionUID = 1L;
}
