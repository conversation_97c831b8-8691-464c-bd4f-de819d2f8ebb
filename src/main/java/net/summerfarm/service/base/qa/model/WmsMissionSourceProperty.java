package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 任务来源属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-298 15:18:36
 */
@Getter
@Setter
@TableName("wms_mission_source_property")
public class WmsMissionSourceProperty implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 来源订单号
     */
    @TableField("source_order_no")
    private String sourceOrderNo;

    /**
     * 来源类型(采购,调拨)
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 来源id
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 任务编号
     */
    @TableField("mission_no")
    private String missionNo;

    /**
     * 任务类型
     */
    @TableField("mission_type")
    private Integer missionType;

    /**
     * 仓库号
     */
    @TableField("warehouse_no")
    private Long warehouseNo;

    /**
     * 城配仓
     */
    @TableField("store_no")
    private Long storeNo;

    /**
     * 预计出库日期
     */
    @TableField("expect_time")
    private LocalDateTime expectTime;

    /**
     * 目标仓库号
     */
    @TableField("target_warehouse_no")
    private Long targetWarehouseNo;


}
