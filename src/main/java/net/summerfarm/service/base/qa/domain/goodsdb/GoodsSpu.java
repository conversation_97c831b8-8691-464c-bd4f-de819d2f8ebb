package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 货品SPU表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_spu")
public class GoodsSpu {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * spu编码
     */
    @TableField(value = "spu")
    private String spu;

    /**
     * 类目id
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * spu名称
     */
    @TableField(value = "title")
    private String title;

    /**
     * spu描述
     */
    @TableField(value = "sub_title")
    private String subTitle;

    /**
     * spu主图
     */
    @TableField(value = "main_picture")
    private String mainPicture;

    /**
     * spu详情图
     */
    @TableField(value = "detail_picture")
    private String detailPicture;

    /**
     * SPU状态：0、使用中 1、已删除
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * saas侧spuId
     */
    @TableField(value = "saas_spu_id")
    private Long saasSpuId;

    /**
     * 鲜沐侧spuId
     */
    @TableField(value = "xm_spu_id")
    private Long xmSpuId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}