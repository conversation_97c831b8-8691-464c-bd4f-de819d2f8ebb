package net.summerfarm.service.base.qa.utils;

import okhttp3.*;
import okio.BufferedSink;
import okio.GzipSink;
import okio.Okio;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class OKHttpUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(OKHttpUtils.class);

    private volatile static OKHttpUtils mInstance;

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded");

    private OkHttpClient mOkHttpClient;

    public OKHttpUtils(OkHttpClient okHttpClient, String host) {
        if (okHttpClient == null) {

            X509TrustManager xtm = new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    X509Certificate[] x509Certificates = new X509Certificate[0];
                    return x509Certificates;
                }
            };

            SSLContext sslContext = null;
            try {
                sslContext = SSLContext.getInstance("SSL");

                sslContext.init(null, new TrustManager[]{xtm}, new SecureRandom());

            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            } catch (KeyManagementException e) {
                e.printStackTrace();
            }
            HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            /** 拦截器压缩http请求体，许多服务器无法解析 */
            Interceptor gzipRequestInterceptor = new Interceptor() {

                @Override
                public Response intercept(Chain chain) throws IOException {
                    Request originalRequest = chain.request();

                    String contentEncode = originalRequest.header("Content-Encoding");
                    if (originalRequest.body() == null
                            || !StringUtils.contains(contentEncode, "gzip")) {
                        return chain.proceed(originalRequest);
                    }
                    Request compressedRequest = originalRequest.newBuilder()
                            .header("Content-Encoding", "gzip")
                            .method(originalRequest.method(), gzip(originalRequest.body()))
                            .build();
                    return chain.proceed(compressedRequest);
                }
            };
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            mOkHttpClient = builder
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .sslSocketFactory(sslContext.getSocketFactory(), xtm)
                    .hostnameVerifier(DO_NOT_VERIFY)
                    .addInterceptor(gzipRequestInterceptor)
//                    .proxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host,80)))
                    .build();
        } else {
            mOkHttpClient = okHttpClient;
        }
    }

    private RequestBody gzip(final RequestBody body) {
        return new RequestBody() {
            @Override
            public MediaType contentType() {
                return body.contentType();
            }

            @Override
            public long contentLength() {
                return -1; // 无法知道压缩后的数据大小
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                BufferedSink gzipSink = Okio.buffer(new GzipSink(sink));
                body.writeTo(gzipSink);
                gzipSink.close();
            }
        };
    }

    public static OKHttpUtils initClient(OkHttpClient okHttpClient, String host) {
        if (mInstance == null) {
            synchronized (OKHttpUtils.class) {
                if (mInstance == null) {
                    mInstance = new OKHttpUtils(okHttpClient, host);
                }
            }
        }
        return mInstance;
    }

    public static OKHttpUtils getInstance(String host) {
        return initClient(null, host);
    }

    /**
     * 发送form类型post请求
     * @param url
     * @param body
     * @return
     */
    public String sendFormPost(String url, String body,Map<String,String>headers) {
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        return sendAndGetResult(FORM, url, body, headers);
    }

    /**
     * 发送put请求
     * @param url
     * @param body
     * @param headers
     * @return
     */
    public String sendCustomJsonPut(String url,String body,Map<String,String>headers){
        headers.put("Content-Type", "application/json;charset=utf-8");
        return sendAndGetResultPut(JSON,url,body,headers);
    }
    public String sendCustomJsonPost(String url,String body,Map<String,String>headers){
        headers.put("Content-Type", "application/json;charset=utf-8");
        return sendAndGetResultPost(JSON,url,body,headers);
    }

    public static void main(String[] args) {
         OKHttpUtils okHttpUtils = OKHttpUtils.getInstance(null);
        String s = okHttpUtils.sendGet("https://www.baidu.com", null, null);
        System.out.println(s);
    }

    public String sendGet(String url,String body,Map<String,String>headers){
        Request.Builder request = new Request.Builder()
                .url(url)
                ;

        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                request.addHeader(key, value);
            });
        }
        try {
            Response response = mOkHttpClient.newCall(request.build()).execute();
            String responseData = response.body().string();
            System.out.println(responseData);
            return responseData;
        } catch (IOException e) {
           throw new RuntimeException(e)
                   ;
        }
    }

    private String sendAndGetResultPut(MediaType mediaType, String url, String body, Map<String, String> headers) {
        RequestBody requestBody = RequestBody.create(mediaType, body);
        Request.Builder requestBuilder = new Request.Builder()
                .put(requestBody)
                .url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                requestBuilder.addHeader(key, value);
            });
        }
        Response response = null;
        try {
            response = mOkHttpClient.newCall(requestBuilder.build()).execute();
        } catch (IOException e) {
            LOGGER.error("action=sendPut, send put http request occur exception", e);
        }
        return okResp2String(response);
    }

    private String sendAndGetResultPost(MediaType mediaType, String url, String body, Map<String, String> headers) {
        RequestBody requestBody = RequestBody.create(mediaType, body);
        Request.Builder requestBuilder = new Request.Builder()
                .post(requestBody)
                .url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                requestBuilder.addHeader(key, value);
            });
        }
        Response response = null;
        try {
            response = mOkHttpClient.newCall(requestBuilder.build()).execute();
        } catch (IOException e) {
            LOGGER.error("action=sendPut, send put http request occur exception", e);
        }
        return okResp2String(response);
    }

    /**
     *发送jsonpost请求
     * @param url
     * @param body
     * @param appKey
     * @param appSecret
     * @param token
     * @return
     */
    public String sendJsonPost(String url, String body, String token, String appKey, String appSecret) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (Objects.isNull(appKey)){
            headers.put("appKey", appKey);
        }
        if (Objects.isNull(appKey)){
            headers.put("appSecret", appSecret);
        }
        headers.put("Token", token);
        return sendAndGetResult(JSON, url, body, headers);
    }


    /**
     * 请求封装
     * @param url
     * @param body
     * @param headers
     * @return
     */
    private String sendAndGetResult(MediaType mediaType, String url, String body, Map<String, String> headers) {
        RequestBody requestBody = RequestBody.create(mediaType, body);
        Request.Builder requestBuilder = new Request.Builder()
                .post(requestBody)
                .url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((key, value) -> {
                if(StringUtils.isEmpty(value)){
                    return;
                }
                requestBuilder.addHeader(key, value);
            });
        }

        Response response = null;
        try {
            response = mOkHttpClient.newCall(requestBuilder.build()).execute();
        } catch (IOException e) {
            LOGGER.error("action=sendPost, send post http request occur exception", e);
        }
        return okResp2String(response);
    }

    private String okResp2String(Response response) {
        String result = null;
        if (response == null) {
            return result;
        }
        try {
            result = response.body().string();
        } catch (IOException e) {
            LOGGER.error("action=okResp2String, send get http request but parse response body occur exception", e);
        }
        return result;
    }
}
