package net.summerfarm.service.base.qa.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.service.log.QueryServiceImpl;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/aiInterface")
@CrossOrigin
public class AIInterfaceController {

    @Resource
    private QueryServiceImpl queryService;

    @GetMapping(value = "/query")
    public JSONObject query(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize){
        log.info("Fetching interface logs with pageNum={}, pageSize={}", pageNum, pageSize);
        JSONObject response = new JSONObject();

        try {
            List<Map<String, Object>> interfaceLogs = queryService.query(pageNum, pageSize);
            long totalCount = queryService.countInterfaceLogs();

            response.put("code", 200);
            response.put("message", "success");
            response.put("success", true);
            response.put("data", interfaceLogs);
            response.put("page", pageNum);
            response.put("size", pageSize);
            response.put("total", totalCount);

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching interface logs: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "Error fetching interface logs: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
        }

        return response;
    }

    @PostMapping(value = "/create")
    public JSONObject createInterfaceLog(@RequestBody String content) {
        log.info("Creating interface log with interfaceName: {}", content);
        JSONObject response = new JSONObject();
        JSONObject tem = JSON.parseObject(content);
        String interfaceName = tem.getString("interface_name");
        try {
            // 验证输入参数
            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                response.put("code", 400);
                response.put("message", "接口名称不能为空");
                response.put("success", false);
                response.put("data", null);
                return response;
            }

            boolean created = queryService.createInterfaceLog(interfaceName.trim());

            if (created) {
                response.put("code", 200);
                response.put("message", "success");
                response.put("success", true);
                response.put("data", "接口日志记录创建成功");
            } else {
                response.put("code", 402);
                response.put("message", "该接口已存在");
                response.put("success", false);
                response.put("data", null);
            }

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error creating interface log: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "创建接口日志记录失败: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
        }

        return response;
    }

    @PostMapping(value = "/update")
    public JSONObject updateInterfaceLog(@RequestBody String content) {
        JSONObject tem = JSON.parseObject(content);
        Long id = tem.getLong("id");
        String interfaceName = tem.getString("interfaceName");
        String context = tem.getString("context");
        log.info("Updating interface log with id: {}, interfaceName: {}", id, interfaceName);
        JSONObject response = new JSONObject();

        try {
            // 验证必需的输入参数
            if (id == null || id <= 0) {
                response.put("code", 400);
                response.put("message", "ID不能为空且必须大于0");
                response.put("success", false);
                response.put("data", null);
                return response;
            }

            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                response.put("code", 400);
                response.put("message", "接口名称不能为空");
                response.put("success", false);
                response.put("data", null);
                return response;
            }

            boolean updated = queryService.updateInterfaceLog(id, interfaceName.trim(), context, 0L, 0L);

            if (updated) {
                response.put("code", 200);
                response.put("message", "success");
                response.put("success", true);
                response.put("data", "接口日志记录更新成功");
            } else {
                response.put("code", 404);
                response.put("message", "记录不存在");
                response.put("success", false);
                response.put("data", null);
            }

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error updating interface log: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "更新接口日志记录失败: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
        }

        return response;
    }

    @PostMapping(value = "/scan")
    public JSONObject scanInterfaceLogs(@RequestParam(value = "interfaceName", required = false) String interfaceName) {
        log.info("Scanning interface logs with interfaceName: {}", interfaceName);
        JSONObject response = new JSONObject();

        try {
            int processedCount = 0;

            if (interfaceName != null && !interfaceName.trim().isEmpty()) {
                // 扫描指定的接口名称
                log.info("Scanning specific interface: {}", interfaceName.trim());
                boolean success = queryService.scanAndUpdateInterfaceLog(interfaceName.trim());

                if (success) {
                    processedCount = 1;
                    response.put("code", 200);
                    response.put("message", "success");
                    response.put("success", true);
                    response.put("data", "接口日志扫描完成，处理了 " + processedCount + " 条记录");
                } else {
                    response.put("code", 404);
                    response.put("message", "指定的接口记录不存在或扫描失败");
                    response.put("success", false);
                    response.put("data", null);
                }
            } else {
                // 扫描所有未扫描的记录
                log.info("Scanning all unscanned interface logs");
                processedCount = queryService.scanAllUnscannedInterfaceLogs();

                response.put("code", 200);
                response.put("message", "success");
                response.put("success", true);
                response.put("data", "批量扫描完成，处理了 " + processedCount + " 条记录");
            }

            // 添加处理数量到响应中
            response.put("processedCount", processedCount);

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error scanning interface logs: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "扫描接口日志失败: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
            response.put("processedCount", 0);
        }

        return response;
    }
}
