package net.summerfarm.service.base.qa.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.service.log.QueryServiceImpl;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/aiInterface")
@CrossOrigin
public class AIInterfaceController {

    @Resource
    private QueryServiceImpl queryService;

    @GetMapping(value = "/query")
    public JSONObject query(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize){
        log.info("Fetching interface logs with pageNum={}, pageSize={}", pageNum, pageSize);
        JSONObject response = new JSONObject();

        try {
            List<Map<String, Object>> interfaceLogs = queryService.query(pageNum, pageSize);
            long totalCount = queryService.countInterfaceLogs();

            response.put("code", 200);
            response.put("message", "success");
            response.put("success", true);
            response.put("data", interfaceLogs);
            response.put("page", pageNum);
            response.put("size", pageSize);
            response.put("total", totalCount);

            return response;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error fetching interface logs: {}", e.getMessage());
            response.put("code", 500);
            response.put("message", "Error fetching interface logs: " + e.getMessage());
            response.put("success", false);
            response.put("data", null);
        }

        return response;
    }
}
