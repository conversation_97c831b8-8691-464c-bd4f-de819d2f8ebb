package net.summerfarm.service.base.qa.service.datachecker.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.base.qa.domain.cosfodb.*;
import net.summerfarm.service.base.qa.mapper.cosfodb.*;
import net.summerfarm.service.base.qa.service.datachecker.ProductPriceService;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProductPriceServiceImpl implements ProductPriceService {

    @Resource
    private ProductSkuPreferentialCostPriceMapper productSkuPreferentialCostPriceMapper;

    @Resource
    private ProductSkuPreferentialCostPriceMappingMapper productSkuPreferentialCostPriceMappingMapper;

    @Resource
    private ProductPricingSupplyCityMappingMapper productPricingSupplyCityMappingMapper;

    @Resource
    private CommonLocationCityMapper commonLocationCityMapper;

    @Resource
    private ProductPricingSupplyMapper productPricingSupplyMapper;

    @Resource
    private MarketItemMapper marketItemMapper;

    @Resource
    private MarketItemPriceMapper marketItemPriceMapper;

    @Resource
    private MerchantAddressMapper merchantAddressMapper;

    @Override
    public void ProductPriceChecker() {
        log.info("开始执行省心订价格巡检接口：ProductPriceChecker");
        //查询 product_sku_preferential_cost_price 找到所有省心订商品，deleted == 0
        LambdaQueryWrapper<ProductSkuPreferentialCostPrice> productSkuPreferentialCostPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productSkuPreferentialCostPriceLambdaQueryWrapper.eq(ProductSkuPreferentialCostPrice::getDeleted, 0);
        List<ProductSkuPreferentialCostPrice> productSkuPreferentialCostPrices = productSkuPreferentialCostPriceMapper.selectList(productSkuPreferentialCostPriceLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(productSkuPreferentialCostPrices)){
            log.info("省心订数据为空，无需检查~~");
            return ;
        }
        productSkuPreferentialCostPrices.forEach(pspcp -> {
            //查询 product_sku_preferential_cost_price_mapping 找到所有城市 sku_preferential_cost_price_id
            LambdaQueryWrapper<ProductSkuPreferentialCostPriceMapping> productSkuPreferentialCostPriceMappingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            productSkuPreferentialCostPriceMappingLambdaQueryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId, pspcp.getId());
            List<ProductSkuPreferentialCostPriceMapping> productSkuPreferentialCostPriceMappings = productSkuPreferentialCostPriceMappingMapper.selectList(productSkuPreferentialCostPriceMappingLambdaQueryWrapper);
            List<Long> cityList = productSkuPreferentialCostPriceMappings.stream().map(a -> a.getCityId()).collect(Collectors.toList());
            //查询是否有有效报价单
            LambdaQueryWrapper<ProductPricingSupply> productPricingSupplyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            productPricingSupplyLambdaQueryWrapper.eq(ProductPricingSupply::getTenantId, pspcp.getTenantId());
            productPricingSupplyLambdaQueryWrapper.eq(ProductPricingSupply::getSupplySkuId, pspcp.getSkuId());
            ProductPricingSupply productPricingSupply = productPricingSupplyMapper.selectOne(productPricingSupplyLambdaQueryWrapper);
            if (Objects.isNull(productPricingSupply)){
                log.info("报价单数据为空，无需检查~~");
                return;
            }
            //过滤有效的报价单的城市
            LambdaQueryWrapper<ProductPricingSupplyCityMapping> productPricingSupplyCityMappingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            productPricingSupplyCityMappingLambdaQueryWrapper.eq(ProductPricingSupplyCityMapping::getProductPricingSupplyId, productPricingSupply.getId());
            List<ProductPricingSupplyCityMapping> productPricingSupplyCityMappings = productPricingSupplyCityMappingMapper.selectList(productPricingSupplyCityMappingLambdaQueryWrapper);
            List<Long> cityIds = productPricingSupplyCityMappings.stream().filter(p -> cityList.contains(p.getCityId())).
                    filter(p1 -> p1.getStartTime().isBefore(LocalDateTime.now()) && p1.getEndTime().isAfter(LocalDateTime.now()))
                    .map(p2 -> p2.getCityId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cityIds)){
                log.info("报价单城市数据为空，无需检查~~");
                return;
            }
            //common_location_city 城市名称
            LambdaQueryWrapper<CommonLocationCity> commonLocationCityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            commonLocationCityLambdaQueryWrapper.in(CommonLocationCity::getId, cityIds);
            List<CommonLocationCity> commonLocationCities = commonLocationCityMapper.selectList(commonLocationCityLambdaQueryWrapper);
            List<String> cityNames = commonLocationCities.stream().map(a -> a.getName()).collect(Collectors.toList());
            LambdaQueryWrapper<MerchantAddress> merchantAddressLambdaQueryWrapper = new LambdaQueryWrapper<>();
            merchantAddressLambdaQueryWrapper.eq(MerchantAddress::getTenantId, pspcp.getTenantId());
            merchantAddressLambdaQueryWrapper.in(MerchantAddress::getCity, cityNames);
            List<MerchantAddress> merchantAddresses = merchantAddressMapper.selectList(merchantAddressLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(merchantAddresses)){
                log.info("省心订对应城市门店数据为空，无需检查~~");
                return;
            }
            //所有应该更新的门店数据信息
            List<Long> storeIds = merchantAddresses.stream().map(a -> a.getStoreId()).collect(Collectors.toList());
            //查询商品是否上架
            LambdaQueryWrapper<MarketItem> marketItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
            marketItemLambdaQueryWrapper.eq(MarketItem::getTenantId, pspcp.getTenantId());
            marketItemLambdaQueryWrapper.eq(MarketItem::getSkuId, pspcp.getSkuId());
            marketItemLambdaQueryWrapper.eq(MarketItem::getDeleteFlag, 1);
            marketItemLambdaQueryWrapper.eq(MarketItem::getOnSale, 1);
            List<MarketItem> marketItems = marketItemMapper.selectList(marketItemLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(marketItems)){
                log.info("在架商品数据为空，无需检查~~");
                return;
            }
            marketItems.forEach(marketItem -> {
                LambdaQueryWrapper<MarketItemPrice> marketItemPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                marketItemPriceLambdaQueryWrapper.eq(MarketItemPrice::getTenantId, pspcp.getTenantId());
                marketItemPriceLambdaQueryWrapper.eq(MarketItemPrice::getMarketItemId, marketItem.getId());
                List<MarketItemPrice> marketItemPrices = marketItemPriceMapper.selectList(marketItemPriceLambdaQueryWrapper);
                //省心订有效
                if (pspcp.getStartTime().isBefore(LocalDateTime.now()) && pspcp.getEndTime().isAfter(LocalDateTime.now())
                        && pspcp.getAvailableQuantity() > 0){
                    boolean result = marketItemPrices.stream().filter(m -> storeIds.contains(m.getTargetId())).allMatch(a -> a.getBasePrice().equals(pspcp.getPrice()));
                    if (!result){
                        String text = "校验失败，省心订生效的时候存在非省心订价格的商品, market_item_id --> tenant_id: " + marketItem.getId() + " -- >" + marketItem.getTenantId();
                        FeishuBotUtil.sendTextMsg(url, text);
                    }
                }else {
                    boolean result = marketItemPrices.stream().filter(m -> storeIds.contains(m.getTargetId())).noneMatch(a -> a.getBasePrice().equals(pspcp.getPrice()));
                    if (!result){
                        String text = "校验失败，省心订失效的时候存在非省心订价格的商品, market_item_id --> tenant_id: \" + marketItem.getId() + \" -- >\" + marketItem.getTenantId()";
                        FeishuBotUtil.sendTextMsg(url, text);
                    }
                }
            });
        });
    }
}
