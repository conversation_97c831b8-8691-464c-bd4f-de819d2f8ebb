package net.summerfarm.service.base.qa.domain.goodsdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2024-02-28 14:36
 * @describe : 
 */
/**
    * 货品属性值表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "goods_property_value")
public class GoodsPropertyValue {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编码（spu/sku/包装）
     */
    @TableField(value = "code")
    private String code;

    /**
     * 类型：1=SPU，2=SKU，3=包装
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 属性id
     */
    @TableField(value = "products_property_id")
    private Long productsPropertyId;

    /**
     * 属性值
     */
    @TableField(value = "products_property_value")
    private String productsPropertyValue;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private Long updater;
}