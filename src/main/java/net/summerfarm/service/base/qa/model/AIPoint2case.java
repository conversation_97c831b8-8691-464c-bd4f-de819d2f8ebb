package net.summerfarm.service.base.qa.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;

/**
 * 类<code>Doc</code>用于：TODO
 *
 * <AUTHOR>
 * @ClassName AIPoint2case
 * @date 2025-04-17
 */
@Document(indexName = "ai_point_case")
@Data
public class AIPoint2case {

    @Id
    private String projectId;

    @Field
    private Object selectedTestPoints;

    @Field
    private String operateTime;

    @Field
    private Object generatedCase;

    public AIPoint2case() {
    }

    public AIPoint2case(String projectId, Object selectedTestPoints, String operateTime, Object generatedCase) {
        this.projectId = projectId;
        this.selectedTestPoints = selectedTestPoints;
        this.operateTime = operateTime;
        this.generatedCase = generatedCase;
    }
}
