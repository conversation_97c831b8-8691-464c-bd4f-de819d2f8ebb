package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 仓储中心
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-220 17:29:26
 */
@Getter
@Setter
@TableName("warehouse_storage_center")
public class WarehouseStorageCenter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仓库编号
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 仓库负责人
     */
    @TableField("manage_admin_id")
    private Integer manageAdminId;

    /**
     * 仓库类型：0、本部仓 1、外部仓 2、合伙人仓
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 仓库所属合伙人
     */
    @TableField("area_manage_id")
    private Integer areaManageId;

    /**
     * 开放状态：0、不开放 1、开放
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 仓库地址
     */
    @TableField("address")
    private String address;

    /**
     * 高德poi
     */
    @TableField("poi_note")
    private String poiNote;

    /**
     * 邮件接收人
     */
    @TableField("mail_to_address")
    private String mailToAddress;

    /**
     * 修改人
     */
    @TableField("updater")
    private Integer updater;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 联系人
     */
    @TableField("person_contact")
    private String personContact;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 所属租户1鲜沐
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 仓库照片
     */
    @TableField("warehouse_pic")
    private String warehousePic;


}
