package net.summerfarm.service.base.qa.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 履约物品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-312 15:20:09
 */
@Getter
@Setter
@TableName("fulfillment_item")
public class FulfillmentItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 履约物品主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部履约物品ID 对应SKU
     */
    @TableField("out_item_id")
    private String outItemId;

    /**
     * 履约单号
     */
    @TableField("fulfillment_no")
    private Long fulfillmentNo;

    /**
     * 履约物品名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 履约物品状态标志: 0不缺货，1缺货
     */
    @TableField("status_tag")
    private Integer statusTag;

    /**
     * 实际履约数量
     */
    @TableField("actual_amount")
    private Integer actualAmount;

    /**
     * 商品规格
     */
    @TableField("weight")
    private String weight;

    /**
     * 履约数量
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 图片
     */
    @TableField("picture")
    private String picture;

    /**
     * 商品属性: 10自营，11代仓
     */
    @TableField("item_attribute")
    private Integer itemAttribute;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 拒收标志：0不拒收，1拒收
     */
    @TableField("refuse_tag")
    private Integer refuseTag;

    /**
     * 库存仓
     */
    @TableField("warehouse_no")
    private Integer warehouseNo;

    /**
     * 逻辑删除标志：0未删除，1已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 商品id
     */
    @TableField("saas_item_id")
    private String saasItemId;

    /**
     * 鲜沐货品code
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 应履约数量
     */
    @TableField("need_delivery_amount")
    private Integer needDeliveryAmount;

    /**
     * 售后数量（正向履约单记录，售后履约单这个字段为0）
     */
    @TableField("after_sale_amount")
    private Integer afterSaleAmount;

    /**
     * 商品二级性质（1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销）
     */
    @TableField("sku_sub_type")
    private Integer skuSubType;

    /**
     * 货品单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 货品重量，单位kg
     */
    @TableField("item_weight")
    private BigDecimal itemWeight;

    /**
     * 货品体积，单位立方米
     */
    @TableField("volume")
    private String volume;

    /**
     * 温区,1:冷冻,2:冷藏,3:常温
     */
    @TableField("temperature")
    private Integer temperature;

    /**
     * 货品类型，0：外部货品
     */
    @TableField("`type`")
    private Integer type;


}
