package net.summerfarm.service.base.qa.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;

/**
 * 类<code>Doc</code>用于：TODO
 * es的 case编辑记录
 * <AUTHOR>
 * @ClassName AICaseEdit
 * @date 2025-04-15
 */
@Document(indexName = "ai_case_edit_record")
@Data
public class AICaseEdit {
    @Field
    private String projectId;


    @Field
    private String operation;

    @Id
    private String subProjectId;
    @Field
    private String originalData;
    @Field
    private String operateTime;
    public AICaseEdit() {
    }

    public AICaseEdit(String projectId, String operation, String subProjectId, String originalData, String operateTime) {
        this.projectId = projectId;
        this.operation = operation;
        this.subProjectId = subProjectId;
        this.originalData = originalData;
        this.operateTime = operateTime;
    }
}
