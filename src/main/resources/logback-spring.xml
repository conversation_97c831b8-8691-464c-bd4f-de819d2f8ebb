<?xml version="1.0" encoding="UTF-8" ?>

<configuration>
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss}] [%p] [%X{xm-rqid}] [%X{EagleEye-TraceID}] [%X{EagleEye-RpcID}] [%X{xm-inbound-flag}] [%c][%M][%L] -> %msg%n
            </pattern>
        </layout>
    </appender>

    <logger name="RocketmqClient" additivity="false">
        <level value="error" />
    </logger>
    <root level="info">
        <appender-ref ref="consoleLog"/>
    </root>
</configuration>
