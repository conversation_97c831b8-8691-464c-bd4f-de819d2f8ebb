#nacos:
#  config:
#    server-addr: test-nacos.summerfarm.net:11000
#    namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
#
#dubbo:
#  application:
#    name: quantity-service
#    id: xmTest
#  registry:
#    protocol: nacos
#    #    address: nacos://************:11000
#    address: nacos://test-nacos.summerfarm.net:11000
#    #    address: nacos://*********:11000
#    parameters:
#      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
#  protocol:
#    id: dubbo
#    name: dubbo
#    port: 20880
#  provider:
#    version: 1.0.0
#    group: online
#    timeout: 5000
#    retries: 0
#    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
#    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
#    corethreads: 10 #核心线程数，默认0
#    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
#    queues: 200 #默认0
#    alive: 30000 #默认60 * 1000ms
#  consumer:
#    version: 1.0.0
#    group: online
#    retries: 0
#    check: false
#    timeout: 10000
#    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
#    corethreads: 10 #核心线程数，默认0
#    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
#    queues: 200 #默认0
#    alive: 30000 #默认60 * 1000ms