<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.service.base.qa.mapper.StockTaskStorageMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.service.base.qa.domain.StockTaskStorage">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="expectTime" column="expect_time" jdbcType="TIMESTAMP"/>
            <result property="inWarehouseNo" column="in_warehouse_no" jdbcType="INTEGER"/>
            <result property="outWarehouseNo" column="out_warehouse_no" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="inWarehouseName" column="in_warehouse_name" jdbcType="VARCHAR"/>
            <result property="outWarehouseName" column="out_warehouse_name" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="category" column="category" jdbcType="TINYINT"/>
            <result property="closeReason" column="close_reason" jdbcType="VARCHAR"/>
            <result property="mismatchReason" column="mismatch_reason" jdbcType="VARCHAR"/>
            <result property="stockTaskId" column="stock_task_id" jdbcType="BIGINT"/>
            <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
            <result property="ownership" column="ownership" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="afterSaleOrderNo" column="after_sale_order_no" jdbcType="VARCHAR"/>
            <result property="fileAddress" column="file_address" jdbcType="VARCHAR"/>
            <result property="closeTime" column="close_time" jdbcType="TIMESTAMP"/>
            <result property="fulfillmentNo" column="fulfillment_no" jdbcType="VARCHAR"/>
            <result property="cargoOwner" column="cargo_owner" jdbcType="VARCHAR"/>
            <result property="arrivalTime" column="arrival_time" jdbcType="TIMESTAMP"/>
            <result property="receivingState" column="receiving_state" jdbcType="INTEGER"/>
            <result property="receiptUrl" column="receipt_url" jdbcType="VARCHAR"/>
            <result property="systemSource" column="system_source" jdbcType="INTEGER"/>
            <result property="psoNo" column="pso_no" jdbcType="VARCHAR"/>
            <result property="uniqueKey" column="unique_key" jdbcType="VARCHAR"/>
            <result property="thirdOrderNo" column="third_order_no" jdbcType="VARCHAR"/>
            <result property="purchaseMode" column="purchase_mode" jdbcType="INTEGER"/>
            <result property="optionFlag" column="option_flag" jdbcType="BIGINT"/>
            <result property="sourceOrderNo" column="source_order_no" jdbcType="VARCHAR"/>
            <result property="externalWarehouseNo" column="external_warehouse_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        admin_id,source_id,type,
        state,expect_time,in_warehouse_no,
        out_warehouse_no,remark,in_warehouse_name,
        out_warehouse_name,process_state,category,
        close_reason,mismatch_reason,stock_task_id,
        operator_name,ownership,tenant_id,
        after_sale_order_no,file_address,close_time,
        fulfillment_no,cargo_owner,arrival_time,
        receiving_state,receipt_url,system_source,
        pso_no,unique_key,third_order_no,
        purchase_mode,option_flag,source_order_no,
        external_warehouse_no
    </sql>
</mapper>
