<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.service.base.qa.mapper.PmsSupplyListMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.service.base.qa.domain.PmsSupplyList">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="spu" column="spu" jdbcType="VARCHAR"/>
            <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
            <result property="warehouseNo" column="warehouse_no" jdbcType="VARCHAR"/>
            <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
            <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="channelType" column="channel_type" jdbcType="TINYINT"/>
            <result property="defaultSupplier" column="default_supplier" jdbcType="TINYINT"/>
            <result property="orderModel" column="order_model" jdbcType="TINYINT"/>
            <result property="fixedTime" column="fixed_time" jdbcType="VARCHAR"/>
            <result property="advanceDay" column="advance_day" jdbcType="INTEGER"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,spu,pd_name,
        warehouse_no,warehouse_name,supplier_id,
        supplier_name,channel_type,default_supplier,
        order_model,fixed_time,advance_day,
        source,creator,updater,
        create_time,update_time,category_id
    </sql>
</mapper>
