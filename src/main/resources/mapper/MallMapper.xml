<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.service.base.qa.mapper.xianmudb.MallMapper">
    <select id="GetMasterNo" resultType="java.lang.String">
        SELECT master_order_no FROM order_relation WHERE  order_no= #{orderNo};
    </select>
    <select id="GetMid" resultType="java.lang.Integer">
        SELECT m_id FROM master_order where master_order_no =#{masterNo};
    </select>
    <select id="GetPhone" resultType="java.lang.String">
        SELECT phone FROM merchant where m_id= #{Mid};
    </select>
    <update id="UpdateRechargeAmount">
        update merchant set recharge_amount=10000000,role_id =-1 where m_id =#{Mid}
    </update>
    <select id="GetOrderType" resultType="java.lang.Integer">
        SELECT `type`  FROM orders where order_no =#{orderNo};
    </select>
</mapper>
