<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.service.base.qa.mapper.xianmudb.TmsMapper">
    <select id="beginSiteId" resultType="java.lang.Integer">
        SELECT id FROM tms_dist_site WHERE out_business_no = #{storeNo} AND type = 1
    </select>

    <update id="updateTmsDeliveryBatch" >
        update tms_delivery_batch
        set
        status = 10
        WHERE
        begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime};
    </update>

    <update id="updateTmsDeliverySite" >
        update tms_delivery_site
        set status=10
        WHERE delivery_batch_id IN
        (SELECT id FROM tms_delivery_batch
        WHERE  begin_site_id =#{beginSiteId}
        AND delivery_time = #{deliveryTime}) AND status != 10;
    </update>

    <delete id="deleteTmsDeliverySiteItem" >
        delete FROM tms_delivery_site_item
        WHERE delivery_site_id IN
        (SELECT id FROM tms_delivery_site
        WHERE delivery_batch_id IN
        (SELECT id FROM tms_delivery_batch
        WHERE begin_site_id =#{beginSiteId}
        AND delivery_time = #{deliveryTime}));
    </delete>

    <delete id="deleteTmsDeliverySiteItemCode" >
        delete FROM tms_delivery_site_item_code
        WHERE delivery_site_item_id IN
        (SELECT id FROM tms_delivery_site_item
        WHERE delivery_site_id IN
        (SELECT id FROM tms_delivery_site
        WHERE delivery_batch_id IN
        (SELECT id FROM tms_delivery_batch
        WHERE begin_site_id = #{beginSiteId}
        AND delivery_time = #{deliveryTime})));
    </delete>

    <delete id="deleteTmsDeliveryPick" >
        delete FROM tms_delivery_pick
        WHERE site_id = @begin_site_id AND delivery_batch_id IN
        (SELECT id FROM tms_delivery_batch WHERE begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime});
    </delete>

    <update id="updateTmsDeliveryOrder" >
        update tms_delivery_order
        set state=10
        WHERE batch_id IN
        (SELECT id FROM tms_delivery_batch WHERE begin_site_id  = #{beginSiteId} AND delivery_time = #{deliveryTime});
    </update>

    <update id="updateTmsDistOrder" >
        update tms_dist_order set state=10
        WHERE id IN (SELECT dist_order_id
        FROM tms_delivery_order WHERE batch_id IN
        (SELECT id FROM tms_delivery_batch WHERE begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime})) AND state != 10;
    </update>

    <delete id="deleteTmsDeliveryItem" >
        delete FROM tms_delivery_item
        WHERE
        delivery_order_id IN (
        SELECT
        id
        FROM
        tms_delivery_order
        WHERE
        batch_id IN (
        SELECT
        id
        FROM
        tms_delivery_batch
        WHERE
        begin_site_id = #{beginSiteId}
        AND delivery_time = #{deliveryTime}))
    </delete>

    <delete id="deleteTmsDeliveryItemCode" >
        delete FROM tms_delivery_item_code
        WHERE delivery_order_item_id IN
        (SELECT id FROM tms_delivery_item WHERE delivery_order_id IN (SELECT id FROM tms_delivery_order WHERE batch_id IN (SELECT id FROM tms_delivery_batch WHERE begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime})));
    </delete>
    <select id="selectTmsDeliveryBatchId" resultType="java.lang.Integer">
        select * from (
        select ifnull(id,'0') as new_Id from tms_delivery_batch where path_id = -1 and begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime}
        union
        select 0
        )A
        where new_Id>(select  if(count(*)=0,-1,0) as new_Id from tms_delivery_batch where path_id = -1 and begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime});
    </select>
    <delete id="deleteTmsDeliverySite" >
        delete from tms_delivery_site
        where
        delivery_batch_id in (
        select
        id
        from
        tms_delivery_batch
        where
        path_id != -1
        and begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime}
        )
        and site_id = #{beginSiteId};
    </delete>

    <update id="updateTmsDeliverySites" >
        update tms_delivery_site
        set delivery_batch_id = #{selectTmsDeliveryBatchId}
        where delivery_batch_id in
        (select id from tms_delivery_batch
        where path_id != -1 and begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime});
    </update>
    <delete id="deleteTmsDeliveryBatch" >
        delete from tms_delivery_batch
        where begin_site_id = #{beginSiteId} AND delivery_time = #{deliveryTime} and path_id != -1;
    </delete>


</mapper>
