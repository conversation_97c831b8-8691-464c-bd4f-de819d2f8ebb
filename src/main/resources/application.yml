server:
  port: 8099
  servlet:
    context-path: /quantity-service
summerfarm:
  manage:
    uri: "https://devadmin.summerfarm.net"

spring:
  application:
    name: quantity-service
  datasource:
    dynamic:
      primary: COSFO_DB
      # 严格匹配数据源,默认false.true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      datasource:
        GOODS_DB:
          url: *******************************************************************************************************************************************************
          driverClassName: com.mysql.cj.jdbc.Driver
          username: test
          password: xianmu619
        COSFO_DB:
          url: *******************************************************************************************************************************************************
          driverClassName: com.mysql.cj.jdbc.Driver
          username: test
          password: xianmu619
        XIANMU_DB:
          url: ******************************************************************************************
          driverClassName: com.mysql.cj.jdbc.Driver
          username: test
          password: xianmu619
        OFC_DB:
          url: **************************************************************************************
          driverClassName: com.mysql.cj.jdbc.Driver
          username: test
          password: xianmu619
        REPEATER:
          url: *****************************************************************************************
          driverClassName: com.mysql.cj.jdbc.Driver
          username: test
          password: xianmu619
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 10 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 5 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  schedulerx2:
    appKey: NczB1mQsBFT7tW6KswhNJw==
    endpoint: acm.aliyun.com
    groupId: local_dev_crm_service_task
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282


mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 配置中心
dubbo:
  application:
    name: quantity-service
    id: xmTest
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms

nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4

elasticsearch:
  rest:
    uris: https://dev.es.summerfarm.net:80
    username: elastic
    password: Xianmu619