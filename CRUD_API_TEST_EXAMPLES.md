# InterfaceLog CRUD API Test Examples

## Test Scenarios

### 1. Create Interface Log Tests

#### Test Case 1: Create New Interface Log (Success)
```bash
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/login"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志记录创建成功"
}
```

#### Test Case 2: Create Duplicate Interface Log (Should Fail)
```bash
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/login"
```

**Expected Response:**
```json
{
  "code": 400,
  "message": "该接口已存在",
  "success": false,
  "data": null
}
```

#### Test Case 3: Create with Empty Interface Name (Should Fail)
```bash
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName="
```

**Expected Response:**
```json
{
  "code": 400,
  "message": "接口名称不能为空",
  "success": false,
  "data": null
}
```

### 2. Update Interface Log Tests

#### Test Case 1: Update Existing Record (Success)
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=1&interfaceName=/api/user/profile&context=User profile endpoint&flag=1&scaned=0"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志记录更新成功"
}
```

#### Test Case 2: Update Non-existent Record (Should Fail)
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=99999&interfaceName=/api/nonexistent"
```

**Expected Response:**
```json
{
  "code": 404,
  "message": "记录不存在",
  "success": false,
  "data": null
}
```

#### Test Case 3: Update with Invalid ID (Should Fail)
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=0&interfaceName=/api/test"
```

**Expected Response:**
```json
{
  "code": 400,
  "message": "ID不能为空且必须大于0",
  "success": false,
  "data": null
}
```

#### Test Case 4: Update with Empty Interface Name (Should Fail)
```bash
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=1&interfaceName="
```

**Expected Response:**
```json
{
  "code": 400,
  "message": "接口名称不能为空",
  "success": false,
  "data": null
}
```

### 3. Scan Interface Logs Tests

#### Test Case 1: Scan Specific Interface (Success)
```bash
curl -X POST "http://localhost:8080/aiInterface/scan" \
  -d "interfaceName=/api/user/login"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "接口日志扫描完成，处理了 1 条记录",
  "processedCount": 1
}
```

#### Test Case 2: Scan Non-existent Interface (Should Fail)
```bash
curl -X POST "http://localhost:8080/aiInterface/scan" \
  -d "interfaceName=/api/nonexistent"
```

**Expected Response:**
```json
{
  "code": 404,
  "message": "指定的接口记录不存在或扫描失败",
  "success": false,
  "data": null,
  "processedCount": 0
}
```

#### Test Case 3: Scan All Unscanned Interfaces (Batch)
```bash
curl -X POST "http://localhost:8080/aiInterface/scan"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "批量扫描完成，处理了 3 条记录",
  "processedCount": 3
}
```

#### Test Case 4: Scan with Empty Interface Name (Batch Mode)
```bash
curl -X POST "http://localhost:8080/aiInterface/scan" \
  -d "interfaceName="
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": "批量扫描完成，处理了 X 条记录",
  "processedCount": "X"
}
```

### 4. Query Interface Logs Tests

#### Test Case 1: Query First Page
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=5"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": [
    {
      "id": 1,
      "interfaceName": "/api/user/login",
      "context": null,
      "flag": 0,
      "scaned": 0
    }
  ],
  "page": 1,
  "size": 5,
  "total": 1
}
```

## Complete Test Flow

### Step 1: Create Multiple Interface Logs
```bash
# Create first interface
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/login"

# Create second interface
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/register"

# Create third interface
curl -X POST "http://localhost:8080/aiInterface/create" \
  -d "interfaceName=/api/user/profile"
```

### Step 2: Query All Records
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=10"
```

### Step 3: Update a Record
```bash
# Assuming the first record has ID=1
curl -X PUT "http://localhost:8080/aiInterface/update" \
  -d "id=1&interfaceName=/api/user/login&context=Updated login endpoint&flag=1&scaned=1"
```

### Step 4: Verify Update
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=10"
```

### Step 5: Scan Specific Interface
```bash
curl -X POST "http://localhost:8080/aiInterface/scan" \
  -d "interfaceName=/api/user/login"
```

### Step 6: Scan All Unscanned Interfaces
```bash
curl -X POST "http://localhost:8080/aiInterface/scan"
```

### Step 7: Verify Scan Results
```bash
curl -X GET "http://localhost:8080/aiInterface/query?pageNum=1&pageSize=10"
```
*Check that scaned field is now 1 and context field contains scan results*

## Testing with Postman

### Create Interface Log
- **Method**: POST
- **URL**: `http://localhost:8080/aiInterface/create`
- **Body**: form-data
  - Key: `interfaceName`, Value: `/api/test/endpoint`

### Update Interface Log
- **Method**: PUT
- **URL**: `http://localhost:8080/aiInterface/update`
- **Body**: form-data
  - Key: `id`, Value: `1`
  - Key: `interfaceName`, Value: `/api/updated/endpoint`
  - Key: `context`, Value: `Updated context`
  - Key: `flag`, Value: `1`
  - Key: `scaned`, Value: `0`

### Query Interface Logs
- **Method**: GET
- **URL**: `http://localhost:8080/aiInterface/query?pageNum=1&pageSize=10`

### Scan Interface Logs
- **Method**: POST
- **URL**: `http://localhost:8080/aiInterface/scan`
- **Body**: form-data
  - Key: `interfaceName`, Value: `/api/user/login` (optional - leave empty for batch scan)

## Expected Database Changes

After running the create tests, the `interface_log` table should contain:

| id | interfaceName | context | flag | scaned |
|----|---------------|---------|------|--------|
| 1  | /api/user/login | null | 0 | 0 |
| 2  | /api/user/register | null | 0 | 0 |
| 3  | /api/user/profile | null | 0 | 0 |

After running the update test on ID=1:

| id | interfaceName | context | flag | scaned |
|----|---------------|---------|------|--------|
| 1  | /api/user/login | Updated login endpoint | 1 | 1 |
| 2  | /api/user/register | null | 0 | 0 |
| 3  | /api/user/profile | null | 0 | 0 |

After running the scan test on `/api/user/login`:

| id | interfaceName | context | flag | scaned |
|----|---------------|---------|------|--------|
| 1  | /api/user/login | {"code":200,"msg":"success","data":[...]} | 1 | 1 |
| 2  | /api/user/register | null | 0 | 0 |
| 3  | /api/user/profile | null | 0 | 0 |

After running batch scan (all unscanned):

| id | interfaceName | context | flag | scaned |
|----|---------------|---------|------|--------|
| 1  | /api/user/login | {"code":200,"msg":"success","data":[...]} | 1 | 1 |
| 2  | /api/user/register | {"code":200,"msg":"success","data":[...]} | 0 | 1 |
| 3  | /api/user/profile | {"code":200,"msg":"success","data":[...]} | 0 | 1 |
