# Wiki Content API Test Examples

## Test Scenarios

### 1. Successful Wiki Content Retrieval

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "新建门店",
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response (Multiple Documents Combined)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "新建门店",
    "content": "=== 新建门店流程 ===\n门店新建流程说明\n1. 选择门店位置\n2. 填写门店信息\n3. 提交审核\n4. 等待审核结果\n\n=== 【2024-07-30】门店进出存 一期 技术方案 ===\n门店进出存技术实现方案\n包含库存管理、进货出货等功能模块\n\n=== 功能熟悉 ===\n门店相关功能介绍和使用说明"
  }
}
```

### 2. Using Default Token (No Token Provided)

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "用户管理"
  }'
```

#### Expected Response (Combined Content from Multiple Matches)
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "用户管理",
    "content": "=== 用户管理系统 ===\n用户管理模块包含以下功能：\n- 用户注册\n- 用户登录\n- 权限管理\n\n=== 商户 ===\n商户管理相关功能\n包含商户注册、审核、权限设置等"
  }
}
```

### 3. Empty Query Parameter

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "",
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 400,
  "message": "查询参数不能为空",
  "success": false,
  "data": null
}
```

### 4. Missing Query Parameter

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 400,
  "message": "查询参数不能为空",
  "success": false,
  "data": null
}
```

### 5. Invalid Token

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "新建门店",
    "token": "invalid_token"
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "新建门店",
    "content": "获取文档内容时发生错误: Feishu API returned error code: 99991663, message: token invalid"
  }
}
```

### 6. No Results Found

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "不存在的内容xyz123",
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "不存在的内容xyz123",
    "content": "未找到相关文档"
  }
}
```

### 7. Missing Token and No Default Configured

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "新建门店"
  }'
```

#### Expected Response (when wiki.token is not configured)
```json
{
  "code": 400,
  "message": "Wiki token未配置或为空",
  "success": false,
  "data": null
}
```

## Postman Test Collection

### Test 1: Successful Request
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: 
  - `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": "新建门店",
  "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
}
```

### Test 2: Default Token Test
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: 
  - `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": "用户管理"
}
```

### Test 3: Error Handling Test
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: 
  - `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": "",
  "token": "invalid_token"
}
```

## JavaScript Test Examples

### Basic Test Function
```javascript
async function testWikiAPI() {
  const testCases = [
    {
      name: "Successful request",
      data: {
        query: "新建门店",
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    },
    {
      name: "Default token test",
      data: {
        query: "用户管理"
      }
    },
    {
      name: "Empty query test",
      data: {
        query: "",
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    },
    {
      name: "Invalid token test",
      data: {
        query: "新建门店",
        token: "invalid_token"
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- Testing: ${testCase.name} ---`);
    
    try {
      const response = await fetch('/aiCase/getWikiContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCase.data)
      });
      
      const result = await response.json();
      console.log('Response:', JSON.stringify(result, null, 2));
      
      if (result.success) {
        console.log('✅ Test passed');
      } else {
        console.log('❌ Test failed (expected for error cases)');
      }
    } catch (error) {
      console.error('❌ Network error:', error);
    }
  }
}

// Run tests
testWikiAPI();
```

### React Component Test Example
```jsx
import React, { useState } from 'react';

function WikiContentTester() {
  const [query, setQuery] = useState('');
  const [token, setToken] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await fetch('/aiCase/getWikiContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: query,
          token: token || undefined
        })
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error: ' + error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Wiki Content API Tester</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <label>Query:</label>
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter search query"
            required
          />
        </div>
        <div>
          <label>Token (optional):</label>
          <input
            type="text"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="Enter Feishu token or leave empty for default"
          />
        </div>
        <button type="submit" disabled={loading}>
          {loading ? 'Searching...' : 'Search Wiki'}
        </button>
      </form>
      
      {result && (
        <div>
          <h3>Result:</h3>
          <pre>{JSON.stringify(result, null, 2)}</pre>
          
          {result.success && result.data && (
            <div>
              <h4>Content:</h4>
              <div style={{ whiteSpace: 'pre-wrap', border: '1px solid #ccc', padding: '10px' }}>
                {result.data.content}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default WikiContentTester;
```

## Expected Feishu API Responses

### Search API Response (Success)
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "has_more": false,
    "items": [
      {
        "node_id": "BAgPwq6lIi5Nykk0E5fcJeabcef",
        "obj_token": "AcnMdexrlokOShxe40Fc0Oabcef",
        "obj_type": 8,
        "parent_id": "",
        "sort_id": 1,
        "space_id": "7307457194084925443",
        "title": "新建门店流程",
        "url": "https://sample.feishu.cn/wiki/BAgPwq6lIi5Nykk0E5fcJeabcef"
      }
    ]
  }
}
```

### Content API Response (Success)
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "content": "门店新建流程\n1. 选择门店位置\n2. 填写门店信息\n3. 提交审核\n4. 等待审核结果\n"
  }
}
```

### Error Response (Invalid Token)
```json
{
  "code": 99991663,
  "msg": "token invalid"
}
```
