# Multi-Query Wiki Content API Test Examples

## Overview

The enhanced `getWikiContent` API now supports both single queries and multiple queries in a single request. This document provides comprehensive test examples for the new functionality.

## Test Scenarios

### 1. Multiple Queries Request (New Feature)

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": ["新建门店", "用户管理", "商户系统"],
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": ["新建门店", "用户管理", "商户系统"],
    "content": "【查询：新建门店】\n=== 新建门店流程 ===\n门店新建的详细步骤说明\n1. 选择门店位置\n2. 填写门店信息\n3. 提交审核申请\n\n=== 【2024-07-30】门店进出存 一期 技术方案 ===\n门店进出存系统的技术实现方案\n\n=== 功能熟悉 ===\n门店管理系统功能介绍\n\n【查询：用户管理】\n=== 用户管理系统 ===\n用户管理模块包含以下功能：\n- 用户注册\n- 用户登录\n- 权限管理\n\n=== 商户 ===\n商户管理相关功能\n\n【查询：商户系统】\n=== 商户管理 ===\n商户系统核心功能介绍\n包含商户注册、审核、权限设置等"
  }
}
```

### 2. Single Query Request (Backward Compatibility)

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "新建门店",
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": ["新建门店"],
    "content": "【查询：新建门店】\n=== 新建门店流程 ===\n门店新建的详细步骤说明\n1. 选择门店位置\n2. 填写门店信息\n3. 提交审核申请\n\n=== 【2024-07-30】门店进出存 一期 技术方案 ===\n门店进出存系统的技术实现方案\n\n=== 功能熟悉 ===\n门店管理系统功能介绍"
  }
}
```

### 3. Using Default Token (Multiple Queries)

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": ["API文档", "技术方案"]
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": ["API文档", "技术方案"],
    "content": "【查询：API文档】\n=== API接口文档 ===\n系统API接口说明文档\n\n【查询：技术方案】\n=== 【2024-07-30】门店进出存 一期 技术方案 ===\n门店进出存系统的技术实现方案\n\n=== 品牌充值预付技术方案设计 ===\n品牌充值预付功能的技术设计方案"
  }
}
```

### 4. Empty Query Array

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": [],
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 400,
  "message": "查询数组不能为空",
  "success": false,
  "data": null
}
```

### 5. Invalid Query Array Element

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": ["新建门店", "", "用户管理"],
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 400,
  "message": "查询数组中的第2个元素无效",
  "success": false,
  "data": null
}
```

### 6. Invalid Query Format

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": 123,
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 400,
  "message": "查询参数格式错误，应为字符串或字符串数组",
  "success": false,
  "data": null
}
```

### 7. Mixed Success and Failure

#### Request
```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": ["新建门店", "不存在的内容xyz123", "用户管理"],
    "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  }'
```

#### Expected Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": ["新建门店", "不存在的内容xyz123", "用户管理"],
    "content": "【查询：新建门店】\n=== 新建门店流程 ===\n门店新建的详细步骤说明\n\n【查询：不存在的内容xyz123】\n未找到相关文档\n\n【查询：用户管理】\n=== 用户管理系统 ===\n用户管理模块包含以下功能"
  }
}
```

## JavaScript Test Examples

### Basic Multi-Query Test
```javascript
async function testMultiQueryWikiAPI() {
  const testCases = [
    {
      name: "Multiple queries test",
      data: {
        query: ["新建门店", "用户管理", "商户系统"],
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    },
    {
      name: "Single query (backward compatibility)",
      data: {
        query: "新建门店",
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    },
    {
      name: "Default token with multiple queries",
      data: {
        query: ["API文档", "技术方案"]
      }
    },
    {
      name: "Empty array test",
      data: {
        query: [],
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    },
    {
      name: "Invalid query format test",
      data: {
        query: 123,
        token: "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- Testing: ${testCase.name} ---`);
    
    try {
      const response = await fetch('/aiCase/getWikiContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCase.data)
      });
      
      const result = await response.json();
      console.log('Response:', JSON.stringify(result, null, 2));
      
      if (result.success) {
        console.log('✅ Test passed');
        if (result.data && result.data.content) {
          console.log('Content preview:', result.data.content.substring(0, 200) + '...');
        }
      } else {
        console.log('❌ Test failed (expected for error cases)');
      }
    } catch (error) {
      console.error('❌ Network error:', error);
    }
  }
}

// Run tests
testMultiQueryWikiAPI();
```

### React Component for Multi-Query Testing
```jsx
import React, { useState } from 'react';

function MultiQueryWikiTester() {
  const [queries, setQueries] = useState(['']);
  const [token, setToken] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const addQuery = () => {
    setQueries([...queries, '']);
  };

  const removeQuery = (index) => {
    const newQueries = queries.filter((_, i) => i !== index);
    setQueries(newQueries.length > 0 ? newQueries : ['']);
  };

  const updateQuery = (index, value) => {
    const newQueries = [...queries];
    newQueries[index] = value;
    setQueries(newQueries);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Filter out empty queries
      const validQueries = queries.filter(q => q.trim() !== '');
      
      const requestData = {
        query: validQueries.length === 1 ? validQueries[0] : validQueries
      };
      
      if (token.trim()) {
        requestData.token = token;
      }
      
      const response = await fetch('/aiCase/getWikiContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error: ' + error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>Multi-Query Wiki Content API Tester</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <h3>Queries:</h3>
          {queries.map((query, index) => (
            <div key={index} style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
              <input
                type="text"
                value={query}
                onChange={(e) => updateQuery(index, e.target.value)}
                placeholder={`Enter query ${index + 1}`}
                style={{ flex: 1, marginRight: '10px', padding: '5px' }}
              />
              {queries.length > 1 && (
                <button type="button" onClick={() => removeQuery(index)}>
                  Remove
                </button>
              )}
            </div>
          ))}
          <button type="button" onClick={addQuery} style={{ marginBottom: '10px' }}>
            Add Query
          </button>
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <label>Token (optional):</label>
          <input
            type="text"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            placeholder="Enter Feishu token or leave empty for default"
            style={{ width: '100%', padding: '5px' }}
          />
        </div>
        
        <button type="submit" disabled={loading}>
          {loading ? 'Searching...' : 'Search Wiki'}
        </button>
      </form>
      
      {result && (
        <div style={{ marginTop: '20px' }}>
          <h3>Result:</h3>
          <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
            {JSON.stringify(result, null, 2)}
          </pre>
          
          {result.success && result.data && result.data.content && (
            <div style={{ marginTop: '10px' }}>
              <h4>Formatted Content:</h4>
              <div style={{ 
                whiteSpace: 'pre-wrap', 
                border: '1px solid #ccc', 
                padding: '10px',
                background: 'white',
                maxHeight: '400px',
                overflow: 'auto'
              }}>
                {result.data.content}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default MultiQueryWikiTester;
```

## Postman Test Collection

### Test 1: Multiple Queries
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": ["新建门店", "用户管理", "商户系统"],
  "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
}
```

### Test 2: Single Query (Backward Compatibility)
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": "新建门店",
  "token": "t-g204f6c27XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
}
```

### Test 3: Error Handling
- **Method**: POST
- **URL**: `http://localhost:8080/aiCase/getWikiContent`
- **Headers**: `Content-Type: application/json`
- **Body** (raw JSON):
```json
{
  "query": ["新建门店", "", "用户管理"],
  "token": "invalid_token"
}
```

## Key Features Tested

1. ✅ **Multiple Query Processing**: Handles arrays of search terms
2. ✅ **Backward Compatibility**: Single string queries still work
3. ✅ **Top 3 Document Limit**: Each query returns max 3 most relevant documents
4. ✅ **Content Organization**: Clear separators between different query results
5. ✅ **Error Handling**: Validates input format and handles individual query failures
6. ✅ **Token Management**: Supports both provided and default tokens
7. ✅ **Partial Success**: Returns results even if some queries fail
