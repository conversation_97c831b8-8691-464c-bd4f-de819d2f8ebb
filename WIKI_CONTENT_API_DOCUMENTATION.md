# Wiki Content API Documentation

## Overview

The `getWikiContent` method in `AICaseAttchmentService` provides functionality to search and retrieve content from Feishu (飞书) Wiki knowledge base. It implements a two-step process: first searching for relevant wiki nodes, then retrieving the actual document content.

## Implementation Details

### Service Class: `AICaseAttchmentService`

**Location**: `src/main/java/net/summerfarm/service/base/qa/service/es/AICaseAttchmentService.java`

### Method Signature

```java
public JSONObject getWikiContent(String query, String token)
```

**Parameters:**
- `query` (String): The search query term
- `token` (String): The authorization Bearer token for Feishu API

**Returns:**
- `JSONObject` containing:
  - `query`: The original search query
  - `content`: The retrieved document content or error message

### API Workflow

#### Step 1: Search Wiki Nodes
- **Endpoint**: `POST https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size=3`
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer {token}`
- **Request Body**:
  ```json
  {
    "query": "{search_term}",
    "space_id": "7259361245839622172"
  }
  ```

#### Step 2: Get Document Content
- **Endpoint**: `GET https://open.feishu.cn/open-apis/docx/v1/documents/{nodeId}/raw_content?lang=0`
- **Headers**:
  - `Authorization: Bearer {token}`

### Constants

```java
private static final String FEISHU_SPACE_ID = "7259361245839622172";
private static final String FEISHU_SEARCH_API = "https://open.feishu.cn/open-apis/wiki/v1/nodes/search?page_size=3";
private static final String FEISHU_CONTENT_API = "https://open.feishu.cn/open-apis/docx/v1/documents/%s/raw_content?lang=0";
```

## REST API Endpoint

### Controller: `AICaseController`

**Endpoint**: `POST /aiCase/getWikiContent`

### Request Format

```json
{
  "query": "新建门店",
  "token": "your_feishu_bearer_token"
}
```

**Parameters:**
- `query` (required): Search term for wiki content
- `token` (optional): Feishu Bearer token. If not provided, uses configured default token from `${wiki.token}`

### Response Format

#### Success Response
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "新建门店",
    "content": "云文档\n多人实时协同，插入一切元素。不仅是在线文档，更是强大的创作和互动工具\n云文档：专为协作而生\n"
  }
}
```

#### Error Responses

**Missing Query Parameter:**
```json
{
  "code": 400,
  "message": "查询参数不能为空",
  "success": false,
  "data": null
}
```

**Missing Token:**
```json
{
  "code": 400,
  "message": "Wiki token未配置或为空",
  "success": false,
  "data": null
}
```

**No Results Found:**
```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "data": {
    "query": "nonexistent_term",
    "content": "未找到相关文档"
  }
}
```

**Server Error:**
```json
{
  "code": 500,
  "message": "获取Wiki内容失败: Error details",
  "success": false,
  "data": null
}
```

## Usage Examples

### cURL Example

```bash
curl -X POST "http://localhost:8080/aiCase/getWikiContent" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "新建门店",
    "token": "your_feishu_bearer_token"
  }'
```

### JavaScript Example

```javascript
async function getWikiContent(query, token) {
  try {
    const response = await fetch('/aiCase/getWikiContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: query,
        token: token
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('Query:', result.data.query);
      console.log('Content:', result.data.content);
      return result.data;
    } else {
      console.error('Error:', result.message);
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}

// Usage
getWikiContent('新建门店', 'your_token_here');
```

### Java Service Usage

```java
@Resource
private AICaseAttchmentService aiCaseAttchmentService;

public void exampleUsage() {
    String query = "新建门店";
    String token = "your_feishu_bearer_token";
    
    JSONObject result = aiCaseAttchmentService.getWikiContent(query, token);
    
    String originalQuery = result.getString("query");
    String content = result.getString("content");
    
    System.out.println("Query: " + originalQuery);
    System.out.println("Content: " + content);
}
```

## Error Handling

The implementation includes comprehensive error handling for:

1. **HTTP Request Failures**: Network connectivity issues, timeouts
2. **API Response Errors**: Invalid tokens, API rate limits, server errors
3. **JSON Parsing Errors**: Malformed responses from Feishu API
4. **Missing Data**: No search results, missing content fields
5. **Parameter Validation**: Empty queries, missing tokens

## Logging

The service provides detailed logging at different levels:

- **INFO**: Successful operations, API calls, results
- **WARNING**: Missing data, empty responses, API errors
- **SEVERE**: Exception details, critical failures

## Configuration

### Required Configuration

Add to your application configuration:

```yaml
# Nacos configuration for wiki token
wiki:
  token: "your_default_feishu_bearer_token"
```

### Dependencies

The implementation uses:
- `OKHttpUtils` for HTTP requests
- `FastJSON` for JSON parsing
- Java `Logger` for logging
- Spring `@Service` annotation for dependency injection

## Security Considerations

1. **Token Security**: Bearer tokens should be stored securely and not logged
2. **Input Validation**: Query parameters are validated to prevent injection attacks
3. **Error Messages**: Sensitive information is not exposed in error responses
4. **Rate Limiting**: Consider implementing rate limiting for API calls

## Performance Notes

1. **Timeout Settings**: HTTP requests have 60-second timeouts configured in OKHttpUtils
2. **Page Size**: Search results are limited to 3 items for performance
3. **Caching**: Consider implementing caching for frequently requested content
4. **Async Processing**: The controller method can be made async for better performance

## Testing

The implementation can be tested using:
1. Unit tests with mocked HTTP responses
2. Integration tests with actual Feishu API (requires valid tokens)
3. Manual testing via the REST endpoint
4. Postman collections for API testing
