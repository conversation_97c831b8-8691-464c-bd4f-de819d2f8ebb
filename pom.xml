<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.13</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>net.summerfarm</groupId>
    <artifactId>service-base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>service-base</name>
    <description>service-base</description>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
        <mysql.connector.version>8.0.22</mysql.connector.version>
        <swagger.version>2.7.0</swagger.version>
        <inventory-sdk.verison>2.0.6-RELEASE</inventory-sdk.verison>
        <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <authentication-sdk.version>1.1.13</authentication-sdk.version>
        <authentication-client.version>1.1.22</authentication-client.version>
<!--        <xianmu-dubbo.version>1.0.10-xRELEASE</xianmu-dubbo.version>-->
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.16</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!--装配testng-->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.4.0</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.connector.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>21.0</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!-- Spring Boot Starter for Dubbo -->
        <!-- https://mvnrepository.com/artifact/org.apache.dubbo/dubbo-spring-boot-starter -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>2.7.8</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>1.0.9</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>net.xianmu.starter</groupId>-->
<!--            <artifactId>xianmu-dubbo-support</artifactId>-->
<!--            <version>1.0.9</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.alibaba.boot/nacos-config-spring-boot-starter -->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.boot</groupId>-->
<!--            <artifactId>nacos-config-spring-boot-starter</artifactId>-->
<!--            <version>0.2.1</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
            <version>1.1.25</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
            <version>${authentication-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <!-- org/mybatis/mybatis/3.5.0 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Nacos Config & Discovery Starter -->
        <!-- https://mvnrepository.com/artifact/com.alibaba.boot/nacos-discovery-spring-boot-starter -->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-discovery-spring-boot-starter</artifactId>
            <version>0.2.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba.nacos/nacos-spring-context -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-boot-starter</artifactId>
            <version>0.2.12</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2021.0.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>3.0.5</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-common</artifactId>
            <version>1.5.5-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>qiniu-java-sdk</artifactId>
                    <groupId>com.qiniu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mysql-connector-java</artifactId>
                    <groupId>mysql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu.common</groupId>
                    <artifactId>xianmu-robot-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>summerfarm-inventory-sdk</artifactId>
            <groupId>net.summerfarm</groupId>
            <version>${inventory-sdk.verison}</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm.wms</groupId>
                    <artifactId>summerfarm-wms-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm.wnc</groupId>
                    <artifactId>summerfarm-wnc-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
            <version>${xianmu-robot-util.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-worker</artifactId>
            <version>1.10.6</version>
        </dependency>
        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-standalone</artifactId>
            <version>2.24.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.lettuce/lettuce-core -->
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>6.1.7.RELEASE</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.19.0</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.15.2</version>
        </dependency>
<!--         https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-data-elasticsearch-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
            <version>2.5.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.aliyun.openservices/aliyun-log -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <version>0.6.69</version>
        </dependency>





    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>net.summerfarm.service.base.qa.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
